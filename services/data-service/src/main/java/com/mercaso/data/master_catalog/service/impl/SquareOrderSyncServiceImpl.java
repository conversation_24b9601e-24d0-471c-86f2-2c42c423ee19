package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.DAYS_IN_ONE_YEAR;
import static com.mercaso.data.master_catalog.constants.SquareConstants.SQUARE_API_LIMIT;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveOrdersRequestDto;
import com.mercaso.data.master_catalog.dto.square.LineItemsDto;
import com.mercaso.data.master_catalog.dto.square.OrdersDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrderLineItem;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.enums.square.SyncEntity;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.SquareOrderSyncService;
import com.mercaso.data.utils.SerializationUtils;
import com.squareup.square.models.SearchOrdersDateTimeFilter;
import com.squareup.square.models.SearchOrdersFilter;
import com.squareup.square.models.SearchOrdersQuery;
import com.squareup.square.models.SearchOrdersSort;
import com.squareup.square.models.TimeRange;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@RequiredArgsConstructor
@Slf4j
@Service
public class SquareOrderSyncServiceImpl implements SquareOrderSyncService {

    private final SquareApiAdapter squareApiAdapter;

    private final MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository;

    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository;

    private final MasterCatalogSquareOrderRepository masterCatalogSquareOrderRepository;

    private final MasterCatalogSquareOrderLineItemRepository masterCatalogSquareOrderLineItemRepository;

    @Transactional
    @Override
    public void syncOrder(SquareDataSyncRequest request, List<String> presentAtLocationIds) {

        if (request.getSyncEntity() != null && request.getSyncEntity() != SyncEntity.ORDER) {
            log.info("Skipping order synchronization for request: {}", request);
            return;
        }

        UUID storeId = request.getStoreId();

        log.info("Synchronized orders starting for store ID: {}", storeId);
        Instant latestSyncTime =
            request.getStartTimestamp() == null ? this.retrieveLatestSyncTimeForOrder(storeId)
            : Instant.ofEpochMilli(request.getStartTimestamp());

        log.info("Querying order that created at after {}", latestSyncTime);
        List<OrdersDto> ordersList = this.fetchOrders(storeId, presentAtLocationIds,
            latestSyncTime);

        if (CollectionUtils.isEmpty(ordersList)) {
            log.info("Synchronized orders, no order found for store ID: {}", storeId);
            return;
        }

        // Save order raw data into DB.
        UUID sourceID = this.saveRawOrderData(ordersList);
        log.info("Save orders raw data, source ID: {}", sourceID);

        // Filter the retried order list by line item and returns. And set orderId with UUID.
        List<OrdersDto> processedOrdersList = this.filterOrderListByCondition(ordersList);

        // Fetch line item information from orders.
        List<LineItemsDto> lineItemsList = this.fetchLineItemsFromOrdersList(processedOrdersList);

        // Build entity for saving line item to DB.
        List<MasterCatalogSquareOrderLineItem> masterCatalogSquareOrderLineItemList = this.buildMasterCatalogSquareOrderLineItemEntities(
            sourceID, storeId, lineItemsList);

        // Build entity for saving order to DB.
        List<MasterCatalogSquareOrder> masterCatalogSquareOrderList = this.buildMasterCatalogSquareOrderEntities(storeId,
            sourceID, processedOrdersList);

        this.saveOrdersList(masterCatalogSquareOrderList);

        this.saveOrderLineItemList(masterCatalogSquareOrderLineItemList);

        log.info("Synchronized orders and order line items successfully for store ID: {}", storeId);

    }

    private Instant retrieveLatestSyncTimeForOrder(UUID storeId) {
        Optional<MasterCatalogSquareOrder> order = masterCatalogSquareOrderRepository
            .findTopByStoreIdOrderByOrderCreatedAtDesc(storeId);
        return order.map(MasterCatalogSquareOrder::getOrderCreatedAt)
            .orElseGet(() -> {
                log.info(
                    "Synchronized orders, no sync record found for stores. Performing full synchronization.");
                return Instant.now().minus(DAYS_IN_ONE_YEAR, ChronoUnit.DAYS);
            });
    }

    private List<OrdersDto> fetchOrders(UUID storeID, List<String> presentAtLocationIds,
        Instant latestSyncTime) {
        BatchRetrieveOrdersRequestDto requestDto = BatchRetrieveOrdersRequestDto.builder()
            .limit(SQUARE_API_LIMIT)
            .locationIds(presentAtLocationIds)
            .createAt(latestSyncTime.toString())
            .query(new SearchOrdersQuery.Builder()
                .filter(new SearchOrdersFilter.Builder()
                    .dateTimeFilter(new SearchOrdersDateTimeFilter.Builder()
                        .createdAt(new TimeRange.Builder()
                            .startAt(latestSyncTime.toString())
                            .build())
                        .build())
                    .build())
                .sort(new SearchOrdersSort.Builder(
                    "CREATED_AT"
                )
                    .sortOrder("DESC").build())
                .build())
            .build();
        return squareApiAdapter.batchRetrieveOrders(storeID, requestDto);

    }

    private List<LineItemsDto> fetchLineItemsFromOrdersList(List<OrdersDto> ordersDtoList) {

        return ordersDtoList.stream()
            .filter(ordersDto -> ordersDto.getLineItems() != null)
            .flatMap(order -> order.getLineItems().stream()
                .peek(lineItem -> {
                    lineItem.setMasterCatalogOrderId(order.getOrderId());
                    lineItem.setCreatedAt(order.getCreatedAt());
                })
            ).toList();

    }

    private List<MasterCatalogSquareOrderLineItem> buildMasterCatalogSquareOrderLineItemEntities(
        UUID sourceID, UUID storeId, List<LineItemsDto> lineItemsDtoList) {

        if (lineItemsDtoList.isEmpty()) {
            log.info("Synchronized order line items , no line items for store {}.", storeId);
            return Collections.emptyList();
        }
        Map<String, UUID> variationMaps = this.findAndBuildVariationIdToRawDataIdMap(
            lineItemsDtoList, storeId);

        List<MasterCatalogSquareOrderLineItem> masterCatalogSquareOrderLineItemList = new ArrayList<>();

        for (LineItemsDto lineItem : lineItemsDtoList) {

            String variationId = lineItem.getCatalogObjectId();
            UUID rawDataId = variationMaps.get(variationId);
            UUID id = UUID.randomUUID();

            if (rawDataId == null) {
                log.warn(
                    "Synchronized order line items, no raw data mapping found for variation ID: {}",
                    variationId);
                continue;
            }
            MasterCatalogSquareOrderLineItem orderLineItem = MasterCatalogSquareOrderLineItem.builder()
                .id(id)
                .masterCatalogOrderId(UUID.fromString(lineItem.getMasterCatalogOrderId()))
                .masterCatalogRawDataId(rawDataId)
                .quantity(Integer.valueOf(lineItem.getQuantity()))
                .createdAt(Instant.parse(lineItem.getCreatedAt()))
                .sourceId(String.valueOf(sourceID))
                .build();
            masterCatalogSquareOrderLineItemList.add(orderLineItem);
            log.debug("Synchronized order line items, built order line items entity: {}",
                orderLineItem);


        }
        return masterCatalogSquareOrderLineItemList;

    }

    private UUID saveRawOrderData(List<OrdersDto> ordersDtoList) {
        MasterCatalogSquareRawData savedMasterCatalogSquareRawData = masterCatalogSquareRawDataRepository.save(
            MasterCatalogSquareRawData.builder()
                .data(SerializationUtils.toTree(ordersDtoList))
                .build());
        return savedMasterCatalogSquareRawData.getId();
    }

    private List<OrdersDto> filterOrderListByCondition(List<OrdersDto> ordersDtoList) {
        return ordersDtoList.stream()
            .filter(orders -> orders.getLineItems() != null | orders.getReturns() == null).toList();
    }

    private List<MasterCatalogSquareOrder> buildMasterCatalogSquareOrderEntities(UUID storeId, UUID sourceId,
        List<OrdersDto> ordersDtoList) {
        List<MasterCatalogSquareOrder> orderList = new ArrayList<>();

        for (OrdersDto order : ordersDtoList) {
            orderList.add(MasterCatalogSquareOrder
                .builder()
                .id(UUID.fromString(order.getOrderId()))
                .state(order.getState())
                .storeId(storeId)
                .orderCreatedAt(Instant.parse(order.getCreatedAt()))
                .orderUpdatedAt(Instant.parse(order.getUpdatedAt()))
                .orderClosedAt(order.getClosedAt() != null ? Instant.parse(order.getClosedAt()) : null)
                .sourceId(String.valueOf(sourceId))
                .build());
            log.debug("Synchronized orders, built order entity: {}", order);

        }
        log.debug("Synchronized orders, total orders entities built: {}", orderList.size());
        return orderList;
    }

    private void saveOrdersList(List<MasterCatalogSquareOrder> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("Synchronized orders, no valid orders to save.");
            return;
        }
        masterCatalogSquareOrderRepository.saveAll(orderList);
        log.info("Synchronized orders, inserted {} orders records.", orderList.size());
    }

    private void saveOrderLineItemList(List<MasterCatalogSquareOrderLineItem> lineItemList) {
        if (CollectionUtils.isEmpty(lineItemList)) {
            log.info("Synchronized order line items, no valid line items to save.");
            return;
        }
        masterCatalogSquareOrderLineItemRepository.saveAll(lineItemList);
        log.info("Synchronized order line items , inserted {} line items records.",
            lineItemList.size());
    }

    private Map<String, UUID> findAndBuildVariationIdToRawDataIdMap(
        List<LineItemsDto> lineItemsDtoList, UUID storeId) {
        List<String> variationIds = lineItemsDtoList.stream()
            .map(LineItemsDto::getCatalogObjectId)
            .collect(Collectors.toList());

        return variationMappingRepository.findAllByVariationIdInAndStoreIdIs(variationIds, storeId)
            .stream()
            .collect(Collectors.toMap(
                MasterCatalogSquareVariationMapping::getVariationId,
                MasterCatalogSquareVariationMapping::getMasterCatalogRawDataId
            ));
    }


}
