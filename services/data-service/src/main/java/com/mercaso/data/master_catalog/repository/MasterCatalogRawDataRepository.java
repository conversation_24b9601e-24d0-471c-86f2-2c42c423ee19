package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Limit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogRawDataRepository extends JpaRepository<MasterCatalogRawData, UUID>,
        JpaSpecificationExecutor<MasterCatalogRawData> {

    @NotNull
    Page<MasterCatalogRawData> findAll(@NotNull Pageable pageable);

    @NotNull
    Page<MasterCatalogRawData> findByStoreIdIs(@NotNull UUID storeId, @NotNull Pageable pageable);

    @NotNull
    Page<MasterCatalogRawData> findByUpcIs(@NotNull String upc, @NotNull Pageable pageable);

    @NotNull
    Page<MasterCatalogRawData> findByUpcIn(@NotNull List<String> upcs, @NotNull Pageable pageable);

    @Query("SELECT m.id FROM MasterCatalogRawData m WHERE m.storeId = :storeId AND m.upc = :upc")
    List<UUID> findIdsByStoreIdAndUpc(@Param("storeId") UUID storeId, @Param("upc") String upc);

    Optional<MasterCatalogRawData> findTopByStoreIdOrderByCreatedAtDesc(UUID storeId);

    List<MasterCatalogRawData> findAllByStoreIdAndUpcIn(UUID storeId, List<String> upcs);

    List<MasterCatalogRawData> findAllByStoreIdAndSourceStatusAndSkuNumberIn(UUID storeId,
            String sourceStatus, List<String> skuNumbers);

    List<MasterCatalogRawData> findByStoreIdAndUpc(UUID storeId, String upc);

    List<MasterCatalogRawData> findAllByStatus(@Size(max = 16) String status, Limit limit);

    Page<MasterCatalogRawData> findAllByStatus(@Size(max = 16) String status, Pageable pageable);

    @Query("SELECT DISTINCT m.skuNumber FROM MasterCatalogRawData m WHERE m.storeId = :storeId AND m.sourceStatus = :sourceStatus AND m.upc IN (:upcs)")
    List<String> findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(UUID storeId,
            String sourceStatus, List<String> upcs);

    List<MasterCatalogRawData> findAllByUpcInOrSkuNumberInOrDescriptionIn(Set<String> upcs,
            Set<String> skus, Set<String> descriptions);

    @Query("SELECT m.upc FROM MasterCatalogRawData m WHERE m.name in(:names) AND m.status = :status")
    List<String> findAllUpcsByNameInAndStatusIs(List<String> names, @Size(max = 16) String status);

    List<MasterCatalogRawData> findAllByStoreId(@NotNull UUID storeId);
}
