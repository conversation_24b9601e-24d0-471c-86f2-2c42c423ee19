package com.mercaso.data.master_catalog.adaptor;

import com.mercaso.wms.client.dto.ResultInventoryStockDto;

import java.util.Optional;

public interface WmsClientAdaptor {
    /**
     * Searches for inventory stock by SKU.
     *
     * @param sku the SKU to search for
     * @return an Optional containing the ResultInventoryStockDto if found, or an empty Optional if not found
     */
    Optional<ResultInventoryStockDto> searchInventoryStockBySku(String sku);
}
