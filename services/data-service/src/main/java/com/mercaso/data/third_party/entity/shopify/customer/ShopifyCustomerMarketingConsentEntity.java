package com.mercaso.data.third_party.entity.shopify.customer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopifyCustomerMarketingConsentEntity {
    private String state;
    @JsonProperty("opt_in_level")
    private String optInLevel;
    @JsonProperty("consent_updated_at")
    private String consentUpdatedAt;
    @JsonProperty("consent_collected_from")
    private String consentCollectedFrom;
}