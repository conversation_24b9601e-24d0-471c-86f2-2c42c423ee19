package com.mercaso.data.third_party.entity.finale;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinaleAvailableStockItemsOnHandEntity {


    private String quantityOnHand;

    @JsonProperty("sublocation")
    private Location subLocation;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Location {

        private String name;

        private String facilityUrl;
    }
}