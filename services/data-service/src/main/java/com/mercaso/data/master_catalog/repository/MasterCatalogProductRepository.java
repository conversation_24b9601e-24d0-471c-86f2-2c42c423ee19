package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface MasterCatalogProductRepository extends JpaRepository<MasterCatalogProduct, UUID>,
    JpaSpecificationExecutor<MasterCatalogProduct> {

    List<MasterCatalogProduct> findAllByUpcIn(List<String> upcs);

    List<MasterCatalogProduct> findAllByMasterCatalogRawDataIdIn(Collection<UUID> masterCatalogRawDataIds);

    @Query("SELECT p.upc, p.name FROM MasterCatalogProduct p WHERE p.name in(:names)")
    List<ProductUpcAndName> findAllUpcsByName(List<String> names);

    interface ProductUpcAndName {
        String getUpc();

        String getName();
    }

}