package com.mercaso.data.master_catalog.controller.v1;


import com.mercaso.data.master_catalog.service.SquareInventoryService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/master-catalog/v1/square-inventory")
public class SquareInventoryResource {

    private final SquareInventoryService squareInventoryService;

    @PreAuthorize("hasAnyAuthority('master-catalog:write:square-inventory')")
    @PostMapping("/upload/{storeId}")
    public ResponseEntity<Void> uploadInventoryAdjustments(
        @PathVariable UUID storeId,
        @RequestParam("file") MultipartFile file
    ) {
        log.info("Received inventory adjustment file for store: {}", storeId);
        squareInventoryService.batchChangeInventory(storeId, file);
        return ResponseEntity.ok().build();
    }
}
