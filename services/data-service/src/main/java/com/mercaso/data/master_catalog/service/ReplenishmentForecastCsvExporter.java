package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.entity.MasterCatalogReplenishmentForecast;
import com.opencsv.CSVWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Optional;

public interface ReplenishmentForecastCsvExporter {

    void writeHeader(CSVWriter csvWriter);

    void writeData(CSVWriter csvWriter, List<MasterCatalogReplenishmentForecast> forecasts, UUID storeId);

    /**
     * Helper method to extract BigDecimal values from metadata
     * 
     * @param metadata the metadata map
     * @param key the key to extract
     * @param defaultValue the default value to use if key doesn't exist
     * @return the extracted value as BigDecimal
     */
    default BigDecimal getBigDecimalValueFromMetadata(Map<String, Object> metadata, String key,
        BigDecimal defaultValue) {
        if (metadata == null || key == null) {
            return defaultValue;
        }
        return Optional.ofNullable(metadata.get(key)).map(value -> {
            if (value instanceof Number) {
                return new BigDecimal(((Number) value).toString());
            } else if (value instanceof String) {
                try {
                    return new BigDecimal((String) value);
                } catch (NumberFormatException e) {
                    return defaultValue;
                }
            }
            return defaultValue;
        }).orElse(defaultValue);
    }

    /**
     * Helper method to extract BigDecimal values from metadata and convert to String Only keeps decimal
     * places when the value is not a whole number or .00
     * 
     * @param metadata the metadata map
     * @param key the key to extract
     * @return the extracted value as String
     */
    default String getBigDecimalAsString(Map<String, Object> metadata, String key) {
        BigDecimal value =
            getBigDecimalValueFromMetadata(metadata, key, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);

        // Check if the value is a whole number or ends with .00
        if (value.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0
            || value.remainder(BigDecimal.ONE).compareTo(new BigDecimal("0.00")) == 0) {
            return value.setScale(0, RoundingMode.HALF_UP).toString();
        }

        return value.toString();
    }
}
