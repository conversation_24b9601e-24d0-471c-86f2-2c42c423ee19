package com.mercaso.data.metrics.service.impl;

import com.mercaso.data.metrics.dto.ItemQuantityDto;
import com.mercaso.data.metrics.dto.MetricsOrderAmountSummaryDto;
import com.mercaso.data.metrics.dto.MetricsOrderDepartmentDistributionDto;
import com.mercaso.data.metrics.dto.MetricsOrderDiscountSummaryDto;
import com.mercaso.data.metrics.dto.MetricsOrderFrequencyDto;
import com.mercaso.data.metrics.dto.MetricsOrderItemRecommendationDto;
import com.mercaso.data.metrics.dto.WeeklyItemSalesDto;
import com.mercaso.data.metrics.mapper.ItemQuantityDtoMapper;
import com.mercaso.data.metrics.mapper.MetricsOrderAmountSummaryDtoMapper;
import com.mercaso.data.metrics.mapper.MetricsOrderDepartmentDistributionDtoMapper;
import com.mercaso.data.metrics.mapper.MetricsOrderDiscountSummaryDtoMapper;
import com.mercaso.data.metrics.mapper.MetricsOrderFrequencyDtoMapper;
import com.mercaso.data.metrics.mapper.MetricsOrderItemRecommendationDtoMapper;
import com.mercaso.data.metrics.repository.MetricsOrderAmountRepository;
import com.mercaso.data.metrics.repository.MetricsOrderDepartmentDistributionRepository;
import com.mercaso.data.metrics.repository.MetricsOrderDiscountRepository;
import com.mercaso.data.metrics.repository.MetricsOrderFrequencyRepository;
import com.mercaso.data.metrics.repository.MetricsOrderItemRecommendationRepository;
import com.mercaso.data.metrics.repository.OrderSkuQuantitySummaryRepository;
import com.mercaso.data.metrics.service.OrderService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {

    //mapper
    private final ItemQuantityDtoMapper itemQuantityDtoMapper;

    private final MetricsOrderItemRecommendationDtoMapper metricsOrderItemRecommendationDtoMapper;

    private final MetricsOrderAmountSummaryDtoMapper metricsOrderAmountSummaryDtoMapper;

    private final MetricsOrderFrequencyDtoMapper metricsOrderFrequencyDtoMapper;

    private final MetricsOrderDepartmentDistributionDtoMapper metricsOrderDepartmentDistributionDtoMapper;

    private final MetricsOrderDiscountSummaryDtoMapper metricsOrderDiscountSummaryDtoMapper;

    //repository
    private final OrderSkuQuantitySummaryRepository orderSkuQuantitySummaryRepository;

    private final MetricsOrderItemRecommendationRepository metricsOrderItemRecommendationRepository;

    private final MetricsOrderAmountRepository metricsOrderAmountRepository;

    private final MetricsOrderFrequencyRepository metricsOrderFrequencyRepository;

    private final MetricsOrderDepartmentDistributionRepository metricsOrderDepartmentDistributionRepository;

    private final MetricsOrderDiscountRepository metricsOrderDiscountRepository;

    //constants
    private static final String WEEKLY = "WEEKLY";

    private static final String FOUR_WEEKLY = "FOUR_WEEKLY";

    @Override
    public WeeklyItemSalesDto getWeeklySkuSalesData() {

        List<ItemQuantityDto> oneWeeklyItemQuantityDtoList = orderSkuQuantitySummaryRepository.findAllByQuantityTypeOrderByQuantityDesc(
            WEEKLY).stream().map(itemQuantityDtoMapper::toDto).toList();

        List<ItemQuantityDto> fourWeeklyItemQuantityDtoList = orderSkuQuantitySummaryRepository.findAllByQuantityTypeOrderByQuantityDesc(
            FOUR_WEEKLY).stream().map(itemQuantityDtoMapper::toDto).toList();


        WeeklyItemSalesDto weeklyItemSalesDto = new WeeklyItemSalesDto();
        weeklyItemSalesDto.setFourWeeklyData(fourWeeklyItemQuantityDtoList);
        weeklyItemSalesDto.setWeeklyData(oneWeeklyItemQuantityDtoList);
        return weeklyItemSalesDto;
    }

    @Override
    public List<MetricsOrderItemRecommendationDto> getItemsRecommendation(String addressId,
        String type, String filterDepartment) {

        return metricsOrderItemRecommendationRepository.findAllByAddressIdAndTypeAndFilterDepartment(addressId,
            type,
            filterDepartment).stream().map(metricsOrderItemRecommendationDtoMapper::toDto).toList();

    }

    @Override
    public List<MetricsOrderAmountSummaryDto> getOrderAmountSummary(String departmentName, String addressId, String timeAggType,
        Integer timeLength) {
        return metricsOrderAmountRepository.findAllByAddressIdAndFilterDepartmentAndDateTypeOrderByDateDesc(addressId,
            departmentName,
            timeAggType).stream().limit(timeLength).map(metricsOrderAmountSummaryDtoMapper::toDto).toList();
    }

    @Override
    public List<MetricsOrderFrequencyDto> getOrderFrequencyV2(String departmentName,
        String addressId,
        String timeAggType,
        Integer timeLength) {

        return metricsOrderFrequencyRepository
            .findAllByAddressIdAndDateTypeOrderByDateDesc(
                addressId,
                timeAggType)
            .stream()
            .limit(timeLength)
            .map(metricsOrderFrequencyDtoMapper::toDto)
            .toList();
    }

    @Override
    public List<MetricsOrderDepartmentDistributionDto> getOrderDepartmentDistribution(String departmentName, String addressId,
        String timeAggType,
        Integer timeLength) {

        return metricsOrderDepartmentDistributionRepository.findAllByAddressIdAndDateTypeAndDateLengthLessThanEqualOrderByDateDesc(
            addressId,
                timeAggType, timeLength)
            .stream()
            .map(metricsOrderDepartmentDistributionDtoMapper::toDto)
            .toList();
    }

    @Override
    public MetricsOrderDiscountSummaryDto getOrderDiscountSummary(String departmentName, String addressId,
        String timeAggType, Integer timeLength) {

        return metricsOrderDiscountRepository.findAllByAddressIdAndDateTypeAndDateLength(addressId, timeAggType, timeLength)
            .stream()
            .map(metricsOrderDiscountSummaryDtoMapper::toDto)
            .findFirst()
            .orElse(null);
    }

}
