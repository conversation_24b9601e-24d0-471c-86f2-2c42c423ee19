package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import com.mercaso.document.operations.models.DocumentResponse;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface DocumentResponseDtoMapper {

    DocumentResponse toExternalDto(DocumentResponseDto dto);

    DocumentResponseDto fromExternalDto(DocumentResponse dto);

}
