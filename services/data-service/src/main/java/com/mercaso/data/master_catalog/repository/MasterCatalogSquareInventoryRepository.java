package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogSquareInventoryRepository extends JpaRepository<MasterCatalogSquareInventory, UUID> {

    Optional<MasterCatalogSquareInventory> findTopByOrderByUpdatedAtDesc();

    List<MasterCatalogSquareInventory> findAllByStateOrderByUpdatedAtDesc(String state);

    @Query("SELECT i FROM MasterCatalogSquareInventory i " +
        "JOIN MasterCatalogRawData mcrd ON i.masterCatalogRawDataId = mcrd.id " +
        "WHERE mcrd.storeId = :storeId " +
        "ORDER BY i.updatedAt DESC" +
        " LIMIT 1")
    Optional<MasterCatalogSquareInventory> findTopByStoreIdAndOrderByUpdatedAtDesc(@Param("storeId") UUID storeId);

    List<MasterCatalogSquareInventory> findAllByMasterCatalogRawDataIdIn(List<UUID> masterCatalogRawDataIdList);

    MasterCatalogSquareInventory findByMasterCatalogRawDataId(UUID rawDataId);
}
