package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.mapper.MasterCatalogRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductGenerationTaskRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RequiredArgsConstructor
@Service
public class MasterCatalogRawDataServiceImpl implements MasterCatalogRawDataService {

    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final MasterCatalogRawDataMapper masterCatalogRawDataMapper;
    private final MasterCatalogImageRepository masterCatalogImageRepository;
    private final ExternalApiAdapter externalApiAdapter;
    private final S3OperationAdapter s3OperationAdapter;
    private final MasterCatalogProductGenerationTaskRepository masterCatalogProductGenerationTaskRepository;

    @Override
    public Page<MasterCatalogRawDataDto> searchMasterCatalogRawData(SearchMasterCatalogRequest searchRequest) {
        PageRequest pageRequest = PageRequest.of(searchRequest.getPage() - 1, searchRequest.getPageSize());

        Page<MasterCatalogRawData> rawData;
        if (StringUtils.isNotBlank(searchRequest.getDescription())) {
            List<String> upcs = externalApiAdapter.getUpcsFromExternalApi(searchRequest.getDescription());
            rawData = masterCatalogRawDataRepository.findByUpcIn(upcs, pageRequest);
        } else {
            rawData = findRawData(searchRequest, pageRequest);
        }

        if (rawData.isEmpty()) {
            return Page.empty();
        }

        Map<UUID, List<String>> imagePathsMap = getImagePathsMap(rawData);

        return rawData.map(rawDataEntity -> mapToDto(rawDataEntity, imagePathsMap));
    }

    @Override
    public Page<MasterCatalogRawDataDto> searchMasterCatalogRawDataByImage(MultipartFile file, Pageable pageable) {
        List<String> upcs = externalApiAdapter.getUpcsFromExternalApiByImage(file);

        Page<MasterCatalogRawData> rawData = masterCatalogRawDataRepository.findByUpcIn(upcs, pageable);

        if (rawData.isEmpty()) {
            return Page.empty();
        }

        Map<UUID, List<String>> imagePathsMap = getImagePathsMap(rawData);

        return rawData.map(rawDataEntity -> mapToDto(rawDataEntity, imagePathsMap));
    }

    @Override
    public void markAsCompleted(Collection<UUID> ids) {
        log.info("Marking raw data as completed: {}", ids);
        
        List<MasterCatalogRawData> rawData = masterCatalogRawDataRepository.findAllById(ids);

        rawData.forEach(data -> data.setStatus(RawDataStatus.COMPLETED.name()));

        masterCatalogRawDataRepository.saveAll(rawData);
    }

  @Override
  public MasterCatalogRawDataDto findById(UUID id) {
    Optional<MasterCatalogRawData> masterCatalogRawData = masterCatalogRawDataRepository.findById(
        id);
    return masterCatalogRawData.map(masterCatalogRawDataMapper::toDto).orElseThrow(() -> {
      log.error("MasterCatalogRawData with id {} not found", id);
      return new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_RAW_DATA_NOT_FOUND);
    });
  }

    private Page<MasterCatalogRawData> findRawData(SearchMasterCatalogRequest searchRequest, PageRequest pageRequest) {
        return switch (searchRequest.getSearchType()) {
            case UPC -> masterCatalogRawDataRepository.findByUpcIs(searchRequest.getUpc(), pageRequest);
            case STORE_ID ->
                masterCatalogRawDataRepository.findByStoreIdIs(UUID.fromString(searchRequest.getStoreId()), pageRequest);
            case ALL -> masterCatalogRawDataRepository.findAll(pageRequest);
        };
    }

    private Map<UUID, List<String>> getImagePathsMap(Page<MasterCatalogRawData> rawData) {
        List<UUID> rawDataIds = rawData.getContent().stream()
            .map(MasterCatalogRawData::getId)
            .toList();

        List<MasterCatalogImage> images = masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(rawDataIds);

        return images
            .stream().peek(image -> {
                String imagePath = image.getImagePath();
                if (!imagePath.startsWith("https://") && !imagePath.startsWith("http://")) {
                    image.setImagePath(s3OperationAdapter.getSignedUrl(imagePath));
                }
            })
            .collect(Collectors.groupingBy(
                MasterCatalogImage::getMasterCatalogRawDataId,
                Collectors.mapping(MasterCatalogImage::getImagePath, Collectors.toList())
            ));
    }

    private MasterCatalogRawDataDto mapToDto(MasterCatalogRawData rawDataEntity, Map<UUID, List<String>> imagePathsMap) {
        MasterCatalogRawDataDto dto = masterCatalogRawDataMapper.toDto(rawDataEntity);
        dto.setImages(imagePathsMap.getOrDefault(rawDataEntity.getId(), List.of()));
        return dto;
    }
}
