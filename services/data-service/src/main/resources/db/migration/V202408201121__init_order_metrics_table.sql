-- create address_info
CREATE TABLE address_info
(
    address_id   VARCHAR(50) PRIMARY KEY,
    address_name VARCHAR(255) NOT NULL,
    salespeople_email   VARCHAR(255) NOT NULL
);

-- create order_summary
CREATE TABLE order_summary
(
    order_id   VARCHAR(100) PRIMARY KEY,
    order_name VARCHAR(100),
    address_id VARCHAR(100),
    amount     NUMERIC(10, 2) NOT NULL,
    date       DATE           NOT NULL,
    items      jsonb,
    deps       jsonb
);

-- create order_frequency
CREATE TABLE order_frequency
(
    address_id VARCHAR(100) PRIMARY KEY,
    year       INT,
    month      INT,
    count      INT
);