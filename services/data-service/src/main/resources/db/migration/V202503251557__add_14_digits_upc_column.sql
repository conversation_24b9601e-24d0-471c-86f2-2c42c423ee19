alter table master_catalog_raw_data add column if not exists upc_14 varchar(14);
alter table master_catalog_product add column if not exists upc_14 varchar(14);

comment on column master_catalog_raw_data.upc_14 is '14 digit UPC code';
comment on column master_catalog_product.upc_14 is '14 digit UPC code';

create index if not exists idx_master_catalog_raw_data_upc_14 on master_catalog_raw_data (upc_14);
create index if not exists idx_master_catalog_product_upc_14 on master_catalog_product (upc_14);

update master_catalog_raw_data set upc_14 = lpad(upc, 14, '0') where upc is not null and upc != '';
update master_catalog_product set upc_14 = lpad(upc, 14, '0');