package com.mercaso.data.recommendation.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.recommendation.dto.ItemRecommendationRecordDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.RecommendationReasonDto;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import com.mercaso.data.recommendation.mapper.ItemRecommendationMapper;
import com.mercaso.data.recommendation.repository.ItemRecommendationRepository;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

@ExtendWith(MockitoExtension.class)
class ItemRecommendationServiceImplTest {

  @Mock
  private ItemRecommendationRepository itemRecommendationRepository;

  @Mock
  private ItemRecommendationMapper itemRecommendationMapper;

  @InjectMocks
  private ItemRecommendationServiceImpl itemRecommendationService;

  private String storeId;
  private Integer pageNumber;
  private Integer pageSize;
  private Pageable pageable;
  private List<ItemRecommendation> recommendations;
  private Page<ItemRecommendation> recommendationPage;
  private List<ItemRecommendationRecordDto> recommendationDtos;

  @BeforeEach
  void setUp() {
    // Initialize test data
    storeId = "store123";
    pageNumber = 0;
    pageSize = 10;
    pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, "reasonValue"));

    // Create test entities
    ItemRecommendation recommendation1 = createRecommendation("sku1", "product1", "PURCHASE_RATE",
        "85.5");
    ItemRecommendation recommendation2 = createRecommendation("sku2", "product2", "VIEW_COUNT",
        "42.7");
    recommendations = Arrays.asList(recommendation1, recommendation2);
    recommendationPage = new PageImpl<>(recommendations, pageable, recommendations.size());

    // Create test DTOs
    ItemRecommendationRecordDto dto1 = createRecommendationDto("sku1", "product1", "PURCHASE_RATE",
        86);
    ItemRecommendationRecordDto dto2 = createRecommendationDto("sku2", "product2", "VIEW_COUNT",
        43);
    recommendationDtos = Arrays.asList(dto1, dto2);
  }

  @Test
  @DisplayName("Should return pageable response with recommendations when search is called")
  void shouldReturnPageableResponseWithRecommendationsWhenSearchIsCalled() {
    // Given
    when(itemRecommendationRepository.searchItemRecommendationByStoreId(anyString(), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationRecordDto> response = itemRecommendationService.search(
        storeId, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(2, response.getTotalElements());

    // Verify repository was called with correct parameters
    verify(itemRecommendationRepository).searchItemRecommendationByStoreId(storeId, pageable);

    // Verify mapper was called for each recommendation
    verify(itemRecommendationMapper).toDto(recommendations.get(0));
    verify(itemRecommendationMapper).toDto(recommendations.get(1));
  }

  @Test
  @DisplayName("Should return empty response when no recommendations found")
  void shouldReturnEmptyResponseWhenNoRecommendationsFound() {
    // Given
    Page<ItemRecommendation> emptyPage = new PageImpl<>(List.of(), pageable, 0);
    when(itemRecommendationRepository.searchItemRecommendationByStoreId(anyString(), any(Pageable.class)))
        .thenReturn(emptyPage);

    // When
    PageableResponse<ItemRecommendationRecordDto> response = itemRecommendationService.search(
        storeId, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(0, response.getData().size());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(0, response.getTotalPages());
    assertEquals(0, response.getTotalElements());

    // Verify repository was called with correct parameters
    verify(itemRecommendationRepository).searchItemRecommendationByStoreId(storeId, pageable);
  }

  /**
   * Helper method to create a test ItemRecommendation entity
   */
  private ItemRecommendation createRecommendation(String sku, String productId, String reason,
      String reasonValue) {
    ItemRecommendation recommendation = new ItemRecommendation();
    recommendation.setStoreId(storeId);
    recommendation.setSkuNumber(sku);
    recommendation.setProductId(productId);
    recommendation.setReason(reason);
    recommendation.setReasonValue(reasonValue);
    return recommendation;
  }

  /**
   * Helper method to create a test ItemRecommendationRecordDto
   */
  private ItemRecommendationRecordDto createRecommendationDto(String sku, String productId,
      String reasonType, Integer reasonValue) {
    RecommendationReasonDto reason = RecommendationReasonDto.builder()
        .type(reasonType)
        .value(reasonValue)
        .build();

    return ItemRecommendationRecordDto.builder()
        .sku(sku)
        .productId(productId)
        .reason(reason)
        .build();
  }
}