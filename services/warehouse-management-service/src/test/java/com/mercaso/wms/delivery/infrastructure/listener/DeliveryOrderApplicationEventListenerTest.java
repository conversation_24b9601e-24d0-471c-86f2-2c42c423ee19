package com.mercaso.wms.delivery.infrastructure.listener;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.InvoiceLineItemData;
import com.mercaso.wms.delivery.application.service.DocumentApplicationService;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.document.DocumentRepository;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.OnesignalAdaptor;
import com.mercaso.wms.delivery.infrastructure.utils.GeneratePdfService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeliveryOrderApplicationEventListenerTest {

    private final GeneratePdfService generatePdfService = mock(GeneratePdfService.class);

    private final DocumentRepository documentRepository = mock(DocumentRepository.class);

    private final DocumentApplicationService documentApplicationService = mock(DocumentApplicationService.class);

    private final OnesignalAdaptor onesignalAdaptor = mock(OnesignalAdaptor.class);

    private final DeliveryTaskRepository deliveryTaskRepository = mock(DeliveryTaskRepository.class);

    private final FeatureFlagsManager featureFlagsManager = mock(FeatureFlagsManager.class);

    private final DeliveryOrderApplicationEventListener listener =
        new DeliveryOrderApplicationEventListener(generatePdfService,
            documentRepository,
            documentApplicationService,
            onesignalAdaptor,
            deliveryTaskRepository,
            featureFlagsManager);

    private List<DeliveryOrderItemDto> createTestItems() {
        DeliveryOrderItemDto item1 = new DeliveryOrderItemDto();
        item1.setSkuNumber("SKU001");
        item1.setTitle("Item 1");
        item1.setDeliveredQty(new BigDecimal("2"));
        item1.setPrice(new BigDecimal("10.5"));
        item1.setQty(new BigDecimal("2"));
        item1.setLine(1);
        item1.setCrvPrice(new BigDecimal("0.5"));
        item1.setPackageSize(6);
        item1.setContainsNicotine(true);

        DeliveryOrderItemDto item2 = new DeliveryOrderItemDto();
        item2.setSkuNumber("SKU002");
        item2.setTitle("Item 2");
        item2.setDeliveredQty(new BigDecimal("1"));
        item2.setPrice(new BigDecimal("20.0"));
        item2.setQty(new BigDecimal("2"));
        item2.setLine(2);
        item2.setCrvPrice(null);
        item2.setPackageSize(null);
        item2.setContainsNicotine(false);
        return Arrays.asList(item1, item2);
    }

    @Test
    void convert_ShouldConvertItemsCorrectly() {
        // Given
        List<DeliveryOrderItemDto> items = createTestItems();

        // When
        List<InvoiceLineItemData> result = listener.convert(items);

        // Then
        assertThat(result).hasSize(2);

        // Verify first item
        InvoiceLineItemData item1 = result.getFirst();
        assertThat(item1.getTitle()).isEqualTo("Item 1");
        assertThat(item1.getCasePriceInUsd()).isEqualTo(10.5);
        assertThat(item1.getSku()).isEqualTo("SKU001");
        assertThat(item1.getQty()).isEqualTo(2.0);
        assertThat(item1.getOrderedQty()).isEqualTo(2.0);
        assertThat(item1.getPositionIdxInCart()).isEqualTo(1);
        assertThat(item1.getRefundedQty()).isEqualTo(0.0);
        assertThat(item1.getAmount()).isEqualTo(21.0);
        assertThat(item1.getCrvPerCase()).isEqualTo(3.0); // 0.5 * 6
        assertThat(item1.getFulfillmentStatus()).isEqualTo("fulfilled");
        assertThat(item1.isProductContainsNicotine()).isTrue();

        // Verify second item
        InvoiceLineItemData item2 = result.get(1);
        assertThat(item2.getTitle()).isEqualTo("Item 2");
        assertThat(item2.getCasePriceInUsd()).isEqualTo(20.0);
        assertThat(item2.getSku()).isEqualTo("SKU002");
        assertThat(item2.getQty()).isEqualTo(1.0);
        assertThat(item2.getOrderedQty()).isEqualTo(2.0);
        assertThat(item2.getPositionIdxInCart()).isEqualTo(2);
        assertThat(item2.getRefundedQty()).isEqualTo(1.0);
        assertThat(item2.getAmount()).isEqualTo(20.0);
        assertThat(item2.getCrvPerCase()).isEqualTo(0.0);
        assertThat(item2.getFulfillmentStatus()).isEqualTo("partial");
        assertThat(item2.isProductContainsNicotine()).isFalse();
    }
} 