package com.mercaso.wms.infrastructure.external.finale.config;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@ConfigurationProperties(prefix = "finale")
public class FinaleConfigProperties {

    @NotBlank
    private String graphqlApiUrl;

    @NotBlank
    private String mfcFacilityUrl;

    @NotBlank
    private String token;

    @NotBlank
    private String locationName;

    @NotBlank
    private String facilityUrl;

    @NotBlank
    private String transferUrl;

    @NotBlank
    private String domain;

    @NotBlank
    private String mfcStage;

    @NotBlank
    private String mdcStage;

    @NotBlank
    private String shipSb;

    @NotBlank
    private String createTransferShipmentUrl;

    @NotBlank
    private String shipTransferShipmentUrl;

    @NotBlank
    private String receiveTransferShipmentUrl;

    @NotBlank
    private String createPurchaseOrderUrl;

    @NotBlank
    private String receivePurchaseOrderUrl;

    @NotBlank
    private String completePurchaseOrderUrl;

    @NotBlank
    private String createShipmentUrl;

    @NotBlank
    private String receiveShipmentUrl;

}