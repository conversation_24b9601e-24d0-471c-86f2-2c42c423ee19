package com.mercaso.wms.infrastructure.statemachine.config;

import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.ASSIGNED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.CANCELED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.COMPLETED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.CREATED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.FAILED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.PARTIALLY_COMPLETED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.PICKED;
import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.PICKING;

import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskTransitionEvents;
import com.mercaso.wms.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.wms.infrastructure.statemachine.factory.WmsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

@Configuration
@EnableStateMachineFactory(contextEvents = false, name = "pickingTaskTransitionEventsStateMachineFactory")
public class PickingTaskStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<PickingTaskStatus, PickingTaskTransitionEvents> {

    @Override
    public void configure(StateMachineStateConfigurer<PickingTaskStatus, PickingTaskTransitionEvents> states)
        throws Exception {
        states
            .withStates()
            .initial(PickingTaskStatus.CREATED)
            .states(EnumSet.allOf(PickingTaskStatus.class))
        ;
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<PickingTaskStatus, PickingTaskTransitionEvents> transitions)
        throws Exception {
        transitions
            // assign
            .withExternal()
            .source(CREATED).target(ASSIGNED)
            .event(PickingTaskTransitionEvents.ASSIGN)
            .and()
            //re-assign
            .withExternal()
            .source(ASSIGNED).target(ASSIGNED)
            .event(PickingTaskTransitionEvents.REASSIGN)
            .and()
            // un-assign
            .withExternal()
            .source(ASSIGNED).target(PickingTaskStatus.CREATED)
            .event(PickingTaskTransitionEvents.UNASSIGN)
            .and()
            // picking
            .withExternal()
            .source(ASSIGNED).target(PICKING)
            .event(PickingTaskTransitionEvents.PICKING)
            .and()
            // picked
            .withExternal()
            .source(ASSIGNED).target(PICKED)
            .event(PickingTaskTransitionEvents.PICK_FINISH)
            .and()
            .withExternal()
            .source(PICKING).target(PICKED)
            .event(PickingTaskTransitionEvents.PICK_FINISH)
            .and()
            .withExternal()
            .source(FAILED).target(PICKED)
            .event(PickingTaskTransitionEvents.PICK_FINISH)
            .and()
            // completed
            .withExternal()
            .source(PICKING).target(COMPLETED)
            .event(PickingTaskTransitionEvents.COMPLETE)
            .and()
            // completed
            .withExternal()
            .source(PICKED).target(COMPLETED)
            .event(PickingTaskTransitionEvents.COMPLETE)
            .and()
            // completed
            .withExternal()
            .source(PARTIALLY_COMPLETED).target(COMPLETED)
            .event(PickingTaskTransitionEvents.COMPLETE)
            .and()
            // failed
            .withExternal()
            .source(ASSIGNED).target(FAILED)
            .event(PickingTaskTransitionEvents.FAIL)
            .and()
            .withExternal()
            .source(PICKING).target(FAILED)
            .event(PickingTaskTransitionEvents.FAIL)
            .and()
            // failed
            .withExternal()
            .source(PICKED).target(FAILED)
            .event(PickingTaskTransitionEvents.FAIL)
            .and()
            .withExternal()
            .source(FAILED).target(FAILED)
            .event(PickingTaskTransitionEvents.FAIL)
            .and()
            .withExternal()
            .source(PARTIALLY_COMPLETED).target(FAILED)
            .event(PickingTaskTransitionEvents.FAIL)
            .and()
            // complete
            .withExternal()
            .source(FAILED).target(PARTIALLY_COMPLETED)
            .event(PickingTaskTransitionEvents.PARTIALLY_COMPLETE)
            .and()
            // complete
            .withExternal()
            .source(FAILED).target(COMPLETED)
            .event(PickingTaskTransitionEvents.COMPLETE)
            .and()
            // picking
            .withExternal()
            .source(FAILED).target(PICKING)
            .event(PickingTaskTransitionEvents.PICKING)
            .and()
            // cancel
            .withExternal()
            .source(ASSIGNED).target(CANCELED)
            .event(PickingTaskTransitionEvents.CANCEL)
            .and()
            //cancel
            .withExternal()
            .source(CREATED).target(CANCELED)
            .event(PickingTaskTransitionEvents.CANCEL)
            .and()
            .withExternal()
            .source(FAILED).target(CANCELED)
            .event(PickingTaskTransitionEvents.CANCEL)
            .and()
            .withExternal()
            .source(PARTIALLY_COMPLETED).target(CANCELED)
            .event(PickingTaskTransitionEvents.CANCEL)
        ;
    }

    @Bean
    @StatemachineFactory(domainClass = PickingTask.class)
    public WmsStateMachineFactory<PickingTaskStatus, PickingTaskTransitionEvents, PickingTask> pickingTaskStateMachineAdapter(
        StateMachineFactory<PickingTaskStatus, PickingTaskTransitionEvents> pickingTaskTransitionEventsStateMachineFactory,
        StateMachinePersister<PickingTaskStatus, PickingTaskTransitionEvents, PickingTask> pickingTaskStateMachinePersister) {
        return new WmsStateMachineFactory<>(pickingTaskTransitionEventsStateMachineFactory, pickingTaskStateMachinePersister);
    }

    @Bean
    public StateMachinePersister<PickingTaskStatus, PickingTaskTransitionEvents, PickingTask> pickingTaskStateMachinePersister(
        StateMachinePersist<PickingTaskStatus, PickingTaskTransitionEvents, PickingTask> pickingTaskStateMachinePersist) {
        return new DefaultStateMachinePersister<>(pickingTaskStateMachinePersist);
    }

}
