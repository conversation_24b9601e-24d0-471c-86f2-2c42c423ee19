package com.mercaso.wms.application.queryservice;

import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.mapper.warehouse.WarehouseDtoApplicationMapper;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WarehouseQueryService {

    private final WarehouseRepository warehouseRepository;

    public List<WarehouseDto> findByType(WarehouseType type) {
        return WarehouseDtoApplicationMapper.INSTANCE.domainToDtos(warehouseRepository.findByType(type));
    }

    public List<WarehouseDto> findAll() {
        return WarehouseDtoApplicationMapper.INSTANCE.domainToDtos(warehouseRepository.findAll());
    }

}
