package com.mercaso.wms.batch.strategy.impl;

import com.mercaso.ims.client.dto.ItemAttributeDto;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.IgnoredOrderDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.strategy.PopulateBatchTemplateStrategy;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Order(1)
@Slf4j
@RequiredArgsConstructor
public class ShopifyOrderPopulateStrategy implements PopulateBatchTemplateStrategy {

    @Override
    public List<ExcelBatchDto> populateBatchTemplate(PopulateCondition condition) {
        log.info("[ShopifyOrderPopulateStrategy] Start to populate batch template");
        if (condition == null || CollectionUtils.isEmpty(condition.getShippingOrders())) {
            log.error("[ShopifyOrderPopulateStrategy] Condition is null or shipping orders are empty");
            return new ArrayList<>();
        }

        removeIgnoredOrders(condition.getIgnoredOrders(), condition.getShippingOrders());

        List<ExcelBatchDto> excelBatchDtos = new ArrayList<>();
        for (ShippingOrder order : condition.getShippingOrders()) {
            if (CollectionUtils.isEmpty(order.getShippingOrderItems())) {
                log.error("[ShopifyOrderPopulateStrategy] Shipping order items are empty for order: {}", order.getId());
                continue;
            }
            for (ShippingOrderItem item : order.getShippingOrderItems()) {
                if (item.getQty() == 0 || item.getSkuNumber() == null) {
                    continue;
                }
                excelBatchDtos.add(createExcelBatchDto(order, item, condition.getItemMap()));
            }
        }
        log.info("[ShopifyOrderPopulateStrategy] Finish to populate batch template");
        return excelBatchDtos;
    }

    private static ExcelBatchDto createExcelBatchDto(ShippingOrder order,
        ShippingOrderItem item,
        Map<String, ItemCategoryDto> itemMap) {
        ExcelBatchDto excelBatchDto = new ExcelBatchDto();
        excelBatchDto.setOrderNumber(order.getOrderNumber());
        excelBatchDto.setItemNumber(item.getSkuNumber());
        excelBatchDto.setQuantity(Objects.requireNonNullElse(item.getQty(), 0));
        if (order.getCustomerAddress() != null && order.getCustomerAddress().getPostalCode() != null
                && order.getCustomerAddress().getPostalCode().length() >= 5) {
            excelBatchDto.setDistrictName(order.getCustomerAddress().getDistrict());
        }
        excelBatchDto.setItemDescription(item.getTitle());
        excelBatchDto.setLine(item.getLine());
        excelBatchDto.setCooler(isCooler(item.getSkuNumber(), itemMap));
        return excelBatchDto;
    }

    private void removeIgnoredOrders(List<IgnoredOrderDto> ignoredOrders, List<ShippingOrder> shippingOrders) {
        if (CollectionUtils.isEmpty(ignoredOrders) || CollectionUtils.isEmpty(shippingOrders)) {
            return;
        }
        shippingOrders.removeIf(order -> ignoredOrders.stream()
                .map(IgnoredOrderDto::getOrderNumber)
            .anyMatch(ignoredOrderNumber -> StringUtils.isNotEmpty(ignoredOrderNumber)
                && ignoredOrderNumber.equals(order.getOrderNumber())));
    }

    private static boolean isCooler(String skuNumber, Map<String, ItemCategoryDto> itemMap) {
        if (StringUtils.isNotEmpty(skuNumber) && !CollectionUtils.isEmpty(itemMap)) {
            ItemCategoryDto itemCategoryDto = itemMap.get(skuNumber);
            if (itemCategoryDto != null && !CollectionUtils.isEmpty(itemCategoryDto.getItemAttributes())) {
                return itemCategoryDto.getItemAttributes().stream()
                    .filter(attr -> BatchConstants.COOLER_ITEM_FLAG_NAME.equals(attr.getAttributeName()))
                    .map(ItemAttributeDto::getValue)
                    .filter(StringUtils::isNotEmpty)
                    .map(Boolean::parseBoolean)
                    .findFirst()
                    .orElse(false);
            }
        }
        return false;
    }
}