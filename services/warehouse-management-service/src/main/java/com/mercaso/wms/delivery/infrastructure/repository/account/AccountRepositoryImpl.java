package com.mercaso.wms.delivery.infrastructure.repository.account;

import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.AccountJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.dataobject.AccountDo;
import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.mapper.AccountDoMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional
public class AccountRepositoryImpl implements AccountRepository {

    private final AccountDoMapper mapper;
    private final AccountJpaDao jpaDao;

    @Override
    public Account save(Account domain) {
        AccountDo accountDo = mapper.domainToDo(domain);
        return mapper.doToDomain(jpaDao.save(accountDo));
    }

    @Override
    public Account findById(UUID id) {
        return jpaDao.findById(id).map(mapper::doToDomain).orElse(null);
    }

    @Override
    public Account update(Account domain) {
        AccountDo existingDo = jpaDao.findById(domain.getId())
            .orElseThrow(() -> new DeliveryBusinessException("Account not found"));

        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(mapper.domainToDo(domain), existingDo, ignoreProperties.toArray(new String[0]));

        return mapper.doToDomain(jpaDao.save(existingDo));
    }

    @Override
    public Optional<Account> findByUserId(UUID userId) {
        return jpaDao.findByUserId(userId).map(mapper::doToDomain);
    }

    @Override
    public Optional<Account> findByEmail(String email) {
        return jpaDao.findByEmail(email).map(mapper::doToDomain);
    }

    @Override
    public List<Account> findAllByEmailIn(Collection<String> strings) {

        List<Account> accounts = new ArrayList<>();
        jpaDao.findAllByEmailIn(strings).forEach(accountDo -> accounts.add(mapper.doToDomain(accountDo)));
        return accounts;
    }

    @Override
    public List<Account> findBy(List<UUID> ids) {
        return jpaDao.findAllById(ids).stream().map(mapper::doToDomain).toList();
    }

    @Override
    public List<Account> findAll() {

        List<Account> accounts = new ArrayList<>();
        jpaDao.findAll().forEach(accountDo -> accounts.add(mapper.doToDomain(accountDo)));
        return accounts;
    }
} 