package com.mercaso.wms.delivery.infrastructure.exception;

import lombok.Getter;

/**
 * Business exception for delivery module.
 */
@Getter
public class DeliveryBusinessException extends RuntimeException {

    private final String code;

    private final String message;

    public DeliveryBusinessException(String message) {
        this.code = null;
        this.message = message;
    }

    public DeliveryBusinessException(String code, String message) {
        super(String.format(message));
        this.code = code;
        this.message = message;
    }

    public DeliveryBusinessException(String message, String code, Object... args) {
        super(String.format(message, args));
        this.code = code;
        this.message = message;
    }

    public DeliveryBusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = null;
        this.message = message;
    }

}