package com.mercaso.wms.infrastructure.repository.warehouse;

import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.warehouse.jpa.WarehouseJpaDao;
import com.mercaso.wms.infrastructure.repository.warehouse.jpa.dataobject.WarehouseDo;
import com.mercaso.wms.infrastructure.repository.warehouse.jpa.mapper.WarehouseDoMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class WarehouseRepositoryImpl implements WarehouseRepository {

    public final WarehouseDoMapper mapper;
    private final WarehouseJpaDao jpaDao;

    @Override
    public Warehouse save(Warehouse domain) {
        WarehouseDo warehouseDo = mapper.domainToDo(domain);
        warehouseDo = jpaDao.save(warehouseDo);
        return mapper.doToDomain(warehouseDo);
    }

    @Override
    public Warehouse findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public Warehouse update(Warehouse domain) {
        WarehouseDo warehouseDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == warehouseDo) {
            throw new WmsBusinessException("Warehouse not found.");
        }
        WarehouseDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
                "createdAt"));
        BeanUtils.copyProperties(target, warehouseDo, ignoreProperties.toArray(new String[0]));
        warehouseDo = jpaDao.save(warehouseDo);
        return mapper.doToDomain(warehouseDo);
    }

    @Override
    public List<Warehouse> findByType(WarehouseType type) {
        List<WarehouseDo> warehouseDos = jpaDao.findByType(type);
        return warehouseDos.stream().map(mapper::doToDomain).toList();
    }

    @Override
    public List<Warehouse> findAll() {
        return mapper.doToDomains(jpaDao.findAll());
    }

    @Override
    public Warehouse findByName(String name) {
        return mapper.doToDomain(jpaDao.findByName(name));
    }

    @Override
    public void deleteAll() {
        jpaDao.deleteAll();
    }
}
