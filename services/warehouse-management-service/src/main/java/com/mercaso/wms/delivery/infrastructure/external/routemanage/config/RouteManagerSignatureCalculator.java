package com.mercaso.wms.delivery.infrastructure.external.routemanage.config;

import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class RouteManagerSignatureCalculator {

    private final RouteManagerProperties routeManagerProperties;
    private static final String HMAC_SHA256 = "HmacSHA256";

    public String computeSignature(String url) {
        String secret = routeManagerProperties.getHmacSecret();
        try {
            Mac hmacSha256 = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            hmacSha256.init(secretKey);

            return Base64.encodeBase64String(hmacSha256.doFinal(url.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException e) {
            log.error("Algorithm not found", e);
            throw new DeliveryBadRequestException("Signature failure");
        } catch (InvalidKeyException e) {
            log.error("Invalid key", e);
            throw new DeliveryBadRequestException("Signature key failure");
        }
    }
}
