package com.mercaso.wms.domain.accountpreference;

import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.command.accountpreference.UpdateAccountPreferenceCommand;
import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class AccountPreference extends BaseDomain {

    private final UUID id;

    private UUID userId;

    private String userName;

    private String email;

    private AccountPreferenceStatus status;

    private Warehouse workWarehouse;

    private Boolean isFullTime;

    private Integer dailyWorkHours;

    private String secretKey;

    private Warehouse externalWarehouse;

    private PickingTaskType taskType;

    private String preferredDepartment;

    private boolean autoAssign;

    public AccountPreference create(CreateAccountPreferenceCommand command,
        Warehouse workWarehouse,
        Warehouse externalWarehouse) {
        this.userName = command.getUserName();
        this.email = command.getEmail();
        this.userId = command.getUserId();
        this.workWarehouse = workWarehouse;
        this.externalWarehouse = externalWarehouse;
        return buildAccountPreference(
            command.getIsFullTime(),
            command.getDailyWorkHours(),
            command.getTaskType(),
            command.getPreferredDepartment(),
            command.getStatus(),
            command.isAutoAssign());
    }

    public AccountPreference createNew(CreateAccountPreferenceCommand command,
        UUID userId,
        String secretKey,
        Warehouse workWarehouse,
        Warehouse externalWarehouse) {
        this.userName = command.getUserName();
        this.email = command.getEmail();
        this.userId = userId;
        this.workWarehouse = workWarehouse;
        this.externalWarehouse = externalWarehouse;
        this.secretKey = secretKey;
        return buildAccountPreference(
            command.getIsFullTime(),
            command.getDailyWorkHours(),
            command.getTaskType(),
            command.getPreferredDepartment(),
            command.getStatus(),
            command.isAutoAssign());
    }

    public AccountPreference update(UpdateAccountPreferenceCommand command,
        Warehouse workWarehouse,
        Warehouse externalWarehouse) {
        this.workWarehouse = workWarehouse;
        this.externalWarehouse = externalWarehouse;
        return buildAccountPreference(
            command.getIsFullTime(),
            command.getDailyWorkHours(),
            command.getTaskType(),
            command.getPreferredDepartment(),
            command.getStatus(),
            command.isAutoAssign());
    }

    @NotNull
    private AccountPreference buildAccountPreference(
        Boolean isFullTime,
        Integer dailyWorkHours,
        PickingTaskType taskType,
        String preferredDepartment,
        AccountPreferenceStatus status,
        boolean autoAssign) {
        this.status = status;
        this.isFullTime = isFullTime;
        this.dailyWorkHours = dailyWorkHours;
        this.taskType = taskType;
        this.preferredDepartment = preferredDepartment;
        this.autoAssign = autoAssign;
        return this;
    }

}
