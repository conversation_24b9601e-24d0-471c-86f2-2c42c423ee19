package com.mercaso.wms.application.mapper.receivingtask;

import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
    uses = ReceivingTaskItemDtoApplicationMapper.class,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReceivingTaskDtoApplicationMapper extends BaseDtoApplicationMapper<ReceivingTask, ReceivingTaskDto> {

    ReceivingTaskDtoApplicationMapper INSTANCE = Mappers.getMapper(ReceivingTaskDtoApplicationMapper.class);
}
