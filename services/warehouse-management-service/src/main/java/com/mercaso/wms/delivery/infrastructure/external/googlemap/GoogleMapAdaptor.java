package com.mercaso.wms.delivery.infrastructure.external.googlemap;

import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.errors.ApiException;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;
import com.mercaso.wms.delivery.infrastructure.external.googlemap.exceptions.GoogleMapException;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoogleMapAdaptor {

    private final GeoApiContext geoApiContext;

    public LatLng getCoordinatesByAddress(String address) {
        try {
            log.debug("Getting coordinates for address: {}", address);
            GeocodingResult[] results = GeocodingApi.geocode(geoApiContext, address)
                .await();

            if (results.length > 0) {
                LatLng location = results[0].geometry.location;
                log.debug("Found coordinates: lat={}, lng={}", location.lat, location.lng);
                return location;
            } else {
                log.warn("No geocoding results found for address: {}", address);
                return null;
            }
        } catch (ApiException e) {
            log.error("Google Maps API error when geocoding address: {}", address, e);
            throw new GoogleMapException("Google Maps API error when geocoding address", e);
        } catch (InterruptedException e) {
            log.error("Geocoding operation interrupted for address: {}", address, e);
            Thread.currentThread().interrupt();
            throw new GoogleMapException("Geocoding operation interrupted", e);
        } catch (IOException e) {
            log.error("I/O error when geocoding address: {}", address, e);
            throw new GoogleMapException("I/O error when geocoding address", e);
        }
    }
}
