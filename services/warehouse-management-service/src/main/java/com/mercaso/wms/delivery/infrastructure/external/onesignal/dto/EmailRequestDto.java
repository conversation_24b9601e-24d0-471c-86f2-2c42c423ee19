package com.mercaso.wms.delivery.infrastructure.external.onesignal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequestDto {

    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("email_to")
    private List<String> emailTo;
    @JsonProperty("template_id")
    private String templateId;
    @JsonProperty("email_from_name")
    private String emailFromName;
    @JsonProperty("custom_data")
    private Map<String, String> customData;
    @JsonProperty("include_unsubscribed")
    private Boolean includeUnsubscribed;

}
