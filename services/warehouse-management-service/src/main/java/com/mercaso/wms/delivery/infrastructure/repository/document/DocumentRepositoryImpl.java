package com.mercaso.wms.delivery.infrastructure.repository.document;

import com.mercaso.wms.delivery.domain.document.Document;
import com.mercaso.wms.delivery.domain.document.DocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.DocumentJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.dataobject.DocumentDo;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.mapper.DocumentDoMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class DocumentRepositoryImpl implements DocumentRepository {

    private final DocumentDoMapper mapper;
    private final DocumentJpaDao jpaDao;

    @Override
    public Document save(Document domain) {
        return mapper.doToDomain(jpaDao.save(mapper.domainToDo(domain)));
    }

    @Override
    public Document findById(UUID id) {
        Optional<DocumentDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public Document update(Document domain) {
        //do not update the document
        return null;
    }

    @Override
    public List<Document> findByEntityIdAndEntityNameAndDocumentTypes(UUID entityId,
        String entityName,
        List<DocumentType> documentTypes) {
        return mapper.doToDomains(jpaDao.findByEntityIdAndEntityNameAndDocumentTypes(entityId, entityName, documentTypes));
    }
}