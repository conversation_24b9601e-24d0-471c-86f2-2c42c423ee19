package com.mercaso.wms.batch.util;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import java.io.File;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

public class ExcelUtil {

    private ExcelUtil() {
    }

    public static <T> List<T> readExcel(File file, Class<T> rowModel, int headerRow, String sheetName) {
        return EasyExcelFactory.read(file, rowModel, new ExcelListener<T>())
            .headRowNumber(headerRow)
            .sheet(sheetName)
            .doReadSync();
    }

    public static void writerSheetToTemplate(List<ExcelBatchDto> excelBatchDtos,
        String department,
        ExcelWriter excelWriter,
        FillConfig fillConfig,
        String sheetName,
        Map<String, String> deliveryDateMap) {
        if (CollectionUtils.isEmpty(excelBatchDtos)) {
            excelBatchDtos = Lists.newArrayList();
        }
        if (department != null) {
            excelBatchDtos = excelBatchDtos.stream()
                .filter(batchDto -> department.equals(batchDto.getDepartment()))
                .toList();
        }
        WriteSheet writeSheet = writerSheet(sheetName).build();
        excelWriter.fill(excelBatchDtos, fillConfig, writeSheet);
        if (deliveryDateMap != null) {
            excelWriter.fill(deliveryDateMap, writeSheet);
        }
    }


}