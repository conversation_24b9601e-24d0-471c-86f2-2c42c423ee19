package com.mercaso.wms.delivery.application.mapper.deliveryorder;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountAllocation;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryOrderItemDtoApplicationMapper extends BaseDtoApplicationMapper<DeliveryOrderItem, DeliveryOrderItemDto> {

    DeliveryOrderItemDtoApplicationMapper INSTANCE = Mappers.getMapper(DeliveryOrderItemDtoApplicationMapper.class);

    @Mapping(target = "discountAllocations", expression = "java(mapDiscountAllocations(domain.getDiscountAllocations()))")
    DeliveryOrderItemDto domainToDto(DeliveryOrderItem domain);

    default List<DiscountAllocation> mapDiscountAllocations(String discountAllocations) {
        if (discountAllocations == null || discountAllocations.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return SerializationUtils.readValue(discountAllocations, new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new DeliveryBusinessException("Failed to deserialize discount applications: " + e.getMessage(), e);
        }
    }

}
