package com.mercaso.wms.infrastructure.excel;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.util.StringUtils;
import com.mercaso.wms.batch.dto.DeliveryDateChangeDto;
import com.mercaso.wms.batch.dto.IgnoredOrderDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.batch.dto.MfcLookupDto;
import com.mercaso.wms.batch.dto.VernonLookupDto;
import com.mercaso.wms.batch.enums.UploadDocNameEnum;
import com.mercaso.wms.batch.mapper.DtoMapper;
import com.mercaso.wms.batch.util.ExcelUtil;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.io.File;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class ReadExcelService {

    public Map<UploadDocNameEnum, List<MfcLookupDto>> getMfcLookUpData(File file) throws WmsBusinessException {
        Map<UploadDocNameEnum, List<MfcLookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        List<MfcLookupDto> mfcLookupDtos = ExcelUtil.readExcel(file, MfcLookupDto.class, 1, UploadDocNameEnum.MASTER.getValue());
        if (validateMfcLookupDtos(mfcLookupDtos)) {
            mfcLookupDtos.removeFirst();
            lookUpData.put(UploadDocNameEnum.MASTER, mfcLookupDtos);
        }
        return lookUpData;
    }

    public boolean isSheetExists(File excelFile, String sheetName) {
        if (excelFile == null || !excelFile.exists()) {
            return false;
        }
        List<ReadSheet> sheets = EasyExcelFactory.read(excelFile).build().excelExecutor().sheetList();
        return sheets.stream().anyMatch(sheet -> sheetName.equals(sheet.getSheetName()));
    }

    public List<VernonLookupDto> getVernonLookupData(File file) throws WmsBusinessException {
        return ExcelUtil.readExcel(file, VernonLookupDto.class, 1, UploadDocNameEnum.VERNON.getValue());
    }

    public List<VernonLookupDto> getExoticLookupData(File file) throws WmsBusinessException {
        return ExcelUtil.readExcel(file, VernonLookupDto.class, 1, UploadDocNameEnum.EXOTIC.getValue());
    }

    public List<DeliveryDateChangeDto> getDeliveryDateChangeDtoList(File file) throws WmsBusinessException {
        if (file == null) {
            return Lists.newArrayList();
        }
        return ExcelUtil.readExcel(file, DeliveryDateChangeDto.class, 2, UploadDocNameEnum.ORDER_DATE_CHANGE.getValue());
    }

    public List<IgnoredOrderDto> getIgnoredOrders(File file) throws WmsBusinessException {
        if (file == null) {
            return Lists.newArrayList();
        }
        return ExcelUtil.readExcel(file, IgnoredOrderDto.class, 1, UploadDocNameEnum.IGNORED_ORDERS.getValue());
    }

    public Map<UploadDocNameEnum, List<LookupDto>> getLookUpData(File file) throws WmsBusinessException {
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        List<UploadDocNameEnum> docNames = List.of(UploadDocNameEnum.COSTCO,
            UploadDocNameEnum.DOWNEY,
            UploadDocNameEnum.MISSION,
            UploadDocNameEnum.MERCASO_PRODUCE);

        List<CompletableFuture<Void>> futures = docNames.stream()
            .map(docNameEnum -> CompletableFuture.runAsync(() -> {
                List<LookupDto> data = ExcelUtil.readExcel(file, LookupDto.class, 4, docNameEnum.getValue());
                if (validateLookupDtos(data, docNameEnum)) {
                    data.removeFirst();
                    synchronized (lookUpData) {
                        lookUpData.put(docNameEnum, data);
                    }
                }
            }))
            .toList();

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new WmsBusinessException("Failed to read lookup data from Excel.", e);
        }

        return lookUpData;
    }

    private boolean validateMfcLookupDtos(List<MfcLookupDto> mfcLookupDtos) {
        if (CollectionUtils.isEmpty(mfcLookupDtos)) {
            log.error("[validateMfcLookupDtos]No MfcLookupDto found.");
            throw new WmsBusinessException("No MfcLookupDto found.");
        } else {
            List<LookupDto> list = mfcLookupDtos.stream().map(DtoMapper.INSTANCE::toLookupDto).toList();
            return validateLookupDtos(list, UploadDocNameEnum.MASTER);
        }
    }

    private boolean validateLookupDtos(List<LookupDto> lookupDtos, UploadDocNameEnum docNameEnum) {
        if (CollectionUtils.isEmpty(lookupDtos)) {
            log.error("[validateLookupDtos]No LookupDto found.");
            throw new WmsBusinessException("No LookupDto found.");
        }

        LookupDto title = lookupDtos.getFirst();
        if (StringUtils.isEmpty(title.getItemNumber()) || StringUtils.isEmpty(title.getItemDescription())
            || StringUtils.isEmpty(title.getDepartment()) || StringUtils.isEmpty(title.getCategory())
            || StringUtils.isEmpty(title.getSubCategory()) || StringUtils.isEmpty(title.getClazz())) {
            log.error("[validateLookupDtos]MfcLookupDto title is invalid.");
            throw new WmsBusinessException("MfcLookupDto title is invalid.");
        }

        Map<String, String> requiredColumns = new HashMap<>(getColumnAndTitleMap(title));

        if (docNameEnum.equals(UploadDocNameEnum.MASTER) || docNameEnum.equals(UploadDocNameEnum.REFRIGERATED)) {
            requiredColumns.put("SKU", title.getItemNumber());
        } else {
            requiredColumns.put("Item # (Mercaso)", title.getItemNumber());
        }
        for (Entry<String, String> entry : requiredColumns.entrySet()) {
            if (!entry.getValue().equalsIgnoreCase(entry.getKey())) {
                log.error("[validateLookupDtos]{} column not found.", entry.getKey());
                throw new WmsBusinessException(entry.getKey() + " column not found.");
            }
        }
        return true;
    }

    @NotNull
    private static Map<String, String> getColumnAndTitleMap(LookupDto title) {
        return Map.of(
            "ITEM DESCRIPTION", title.getItemDescription(),
            "DEPARTMENT", title.getDepartment(),
            "CATEGORY", title.getCategory(),
            "SUB-CATEGORY", title.getSubCategory(),
            "CLASS", title.getClazz()
        );
    }

}
