package com.mercaso.wms.domain.inventorystock;

import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockTransitionEvents;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockItemsOnHandDto;
import com.mercaso.wms.infrastructure.statemachine.BaseStateMachine;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class InventoryStock extends BaseStateMachine<InventoryStock, InventoryStockStatus, InventoryStockTransitionEvents> {

    private UUID id;

    private Warehouse warehouse;

    private Item item;

    private Location location;

    private String lotNumber;

    private LocalDate productionDate;

    private LocalDate expirationDate;

    private BigDecimal qty;

    private BigDecimal reservedQty;

    private BigDecimal availableQty;

    private String lpnNumber;

    private UUID vendorId;

    public InventoryStock create(CreateInventoryStockCommand command, Warehouse warehouse, Location location) {
        this.warehouse = warehouse;
        this.item = Item.builder().id(command.getItemId()).skuNumber(command.getSkuNumber()).title(command.getTitle()).build();
        this.location = location;
        this.lotNumber = command.getLotNumber();
        this.productionDate = command.getProductionDate();
        this.expirationDate = command.getExpirationDate();
        this.qty = command.getQty();
        this.reservedQty = BigDecimal.ZERO;
        this.availableQty = command.getQty();
        this.lpnNumber = command.getLpnNumber();
        this.setStatus(InventoryStockStatus.AVAILABLE);
        return this;
    }

    public InventoryStock create(FinaleAvailableStockItemsOnHandDto stockItemsOnHandDto,
        String sku,
        Warehouse warehouse,
        Location location) {
        this.warehouse = warehouse;
        this.item = Item.builder().skuNumber(sku).build();
        this.location = location;
        this.qty = BigDecimal.valueOf(stockItemsOnHandDto.getQuantityOnHand());
        this.reservedQty = BigDecimal.ZERO;
        this.availableQty = BigDecimal.valueOf(stockItemsOnHandDto.getQuantityOnHand());
        this.setStatus(InventoryStockStatus.AVAILABLE);
        return this;
    }

    public void update(FinaleAvailableStockItemsOnHandDto stockItemsOnHandDto) {
        this.qty = BigDecimal.valueOf(stockItemsOnHandDto.getQuantityOnHand());
        this.availableQty = BigDecimal.valueOf(stockItemsOnHandDto.getQuantityOnHand());
    }

    public void delete() {
        this.qty = BigDecimal.ZERO;
        this.availableQty = BigDecimal.ZERO;
        this.setDeletedAt(Instant.now());
        this.setDeletedBy("Finale");
    }

    public void available() {
        if (this.getState() != InventoryStockStatus.AVAILABLE) {
            this.processEvent(InventoryStockTransitionEvents.AVAILABLE);
        }
    }

    public void unavailable() {
        if (this.getState() != InventoryStockStatus.UNAVAILABLE) {
            this.processEvent(InventoryStockTransitionEvents.UNAVAILABLE);
        }
    }

}
