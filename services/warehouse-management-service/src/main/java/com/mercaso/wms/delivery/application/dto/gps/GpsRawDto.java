package com.mercaso.wms.delivery.application.dto.gps;

import com.mercaso.wms.application.dto.BaseDto;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GpsRawDto extends BaseDto {

    private UUID id;
    private UUID deliveryTaskId;
    private UUID deliveryOrderId;
    private UUID userId;
    private String userName;
    private Double latitude;
    private Double longitude;
    private BigDecimal accuracy;
    private BigDecimal speed;
    private String heading;
    private Instant reportAt;

}
