package com.mercaso.wms.interfaces;

import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.command.accountpreference.UpdateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.application.service.AccountPreferenceApplicationService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/account-preferences")
@RequiredArgsConstructor
public class AccountPreferenceResource {

    private final AccountPreferenceApplicationService accountPreferenceApplicationService;

    @PreAuthorize("hasAuthority('wms:write:account-preference')")
    @PostMapping()
    public AccountPreferenceDto create(@RequestBody CreateAccountPreferenceCommand command) {
        log.info("Account preference creation requested: {}", command);
        return accountPreferenceApplicationService.createAccountPreference(command);
    }

    @PreAuthorize("hasAuthority('wms:write:account-preference')")
    @PutMapping("/{id}")
    public AccountPreferenceDto update(@PathVariable UUID id, @RequestBody UpdateAccountPreferenceCommand command) {
        log.info("Account preference update requested: {} {}", id, command);
        return accountPreferenceApplicationService.updateAccountPreference(command, id);
    }

}
