package com.mercaso.wms.delivery.infrastructure.repository.account.jpa;

import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.dataobject.AccountDo;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface AccountJpaDao extends JpaRepository<AccountDo, UUID> {

    Optional<AccountDo> findByUserId(UUID userId);

    Optional<AccountDo> findByEmail(String email);

    List<AccountDo> findAllByEmailIn(Collection<String> emails);
}