package com.mercaso.wms.delivery.application.dto.invoice;

import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountAllocation;
import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountApplication;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Value;

@EqualsAndHashCode
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenerateInvoiceDto {

    UUID deliveryOrderId;
    String orderNumber;
    String orderDate;
    CustomerDetailData customerDetail;
    AddressDetailData shippingDetail;
    SummaryData summaryData;
    List<InvoiceLineItemData> lineItems;
    List<DiscountApplication> discountApplications;
    OrderNoteData orderNote;
    OrderNoteData driverOrderNote;
    OrganizationData organization;
    String paymentType;
    String signature;
    String driverName;

    @Builder
    @Value
    public static class OrganizationData {

        String name;
    }

    @Builder
    @Value
    public static class CustomerDetailData {

        String fullName;
        String email;
    }

    @Builder
    @Value
    public static class AddressDetailData {

        String name;
        String address;
        String company;
        String city;
        String zip;
        String provinceCode;
        String phoneNumber;
    }

    @Data
    @Builder
    @NoArgsConstructor(force = true)
    @AllArgsConstructor
    public static class InvoiceLineItemData {

        String title;
        double casePriceInUsd;
        double qty;
        double orderedQty;
        Integer positionIdxInCart;
        double refundedQty;
        double amount;
        String sku;
        Double crvPerCase;
        String fulfillmentStatus;
        boolean productContainsNicotine;
        List<DiscountAllocation> discountAllocations;
    }

    @Data
    @Builder
    public static class OrderNoteData {

        String note;
    }

    @Data
    @Builder
    @NoArgsConstructor(force = true)
    @AllArgsConstructor
    public static class SummaryData {

        double paid;
    }
}
