package com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.mapper;

import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.dataobject.CustomerDo;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerDoMapper extends BaseDoMapper<CustomerDo, Customer> {

} 