package com.mercaso.wms.delivery.infrastructure.external.routemanage.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ExecutionEvent {

    @JsonProperty("type")
    private String type;

    @JsonProperty("orderId")
    private String orderId;

    @JsonProperty("orderStepType")
    private String orderStepType;

    @JsonProperty("date")
    private String date;

    @JsonProperty("vehicleId")
    private String vehicleId;

    @JsonProperty("driverId")
    private String driverId;

    @JsonProperty("data")
    private Object data;
}
