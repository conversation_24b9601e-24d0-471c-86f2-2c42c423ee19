ALTER table picking_task_items
    add COLUMN IF NOT EXISTS location_id UUID;

UPDATE picking_task_items pti
SET location_id = l.id
FROM location l
WHERE pti.location_name = l.name;

UPDATE location
SET aisle_number = CASE
                       WHEN aisle_number LIKE '%.RD-' THEN LEFT(aisle_number, LENGTH(aisle_number) - 4)
                       ELSE name
    END
WHERE aisle_number IS NULL;

UPDATE location
SET aisle_number = CASE
                       WHEN aisle_number = '0' THEN '00'
                       WHEN aisle_number = '1' THEN '01'
                       WHEN aisle_number = '2' THEN '02'
                       WHEN aisle_number = '3' THEN '03'
                       WHEN aisle_number = '4' THEN '04'
                       WHEN aisle_number = '5' THEN '05'
                       WHEN aisle_number = '6' THEN '06'
                       WHEN aisle_number = '7' THEN '07'
                       WHEN aisle_number = '8' THEN '08'
                       ELSE aisle_number
    END;