package com.mercaso.partnerinsight.utils.interfacesutils;

import com.mercaso.partnerinsight.dto.TenantStoreInfoDto;
import com.mercaso.partnerinsight.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class TenantStoreInfoRestApiUtil extends IntegrationTestRestUtil {
    private static final String GET_TENANT_STORE_INFO_LIST = "/v1/tenant-store-info";


    public TenantStoreInfoRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<TenantStoreInfoDto> findAllByTenantId() throws Exception {
        return getEntityList(GET_TENANT_STORE_INFO_LIST, TenantStoreInfoDto.class);
    }


}