<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Log Pattern Definitions -->
    <property name="log.pattern"
        value="%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{10}) - [%tid] - [%X{user-id}] %cyan(%msg%n)"/>

    <!-- Plain pattern for SkyWalking (no color codes) -->
    <property name="skywalking.log.pattern"
        value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{10} - [%tid] - [%X{user-id}] %msg%n"/>

    <!-- Console Output Configuration -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>

    <!-- SkyWalking Log Collection -->
    <appender name="SKYWALKING" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${skywalking.log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>

    <!-- Framework Loggers -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.hibernate" level="INFO"/>
    <logger name="org.apache" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="reactor" level="INFO"/>
    <logger name="org.mongodb" level="INFO"/>
    <logger name="org.elasticsearch" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="org.quartz" level="INFO"/>
    <logger name="springfox.documentation" level="INFO"/>
    <logger name="org.redisson" level="INFO"/>

    <!-- Spring Web MVC Specific Loggers -->
    <logger name="org.springframework.web.servlet.mvc.method.annotation" level="INFO"/>
    <logger name="org.springframework.web.servlet.handler" level="INFO"/>
    <logger name="org.springframework.web.servlet" level="INFO"/>
    <logger name="org.springdoc" level="INFO"/>

    <!-- Additional Framework Loggers -->
    <logger name="org.jboss" level="INFO"/>
    <logger name="ch.qos.logback" level="INFO"/>
    <logger name="org.apache.catalina" level="INFO"/>
    <logger name="org.apache.coyote" level="INFO"/>
    <logger name="org.apache.tomcat" level="INFO"/>
    <logger name="_.s.w.s.H.Mappings" level="INFO"/>
    <logger name="com.okta" level="INFO"/>

    <!-- Swagger/OpenAPI Loggers -->
    <logger name="springfox.documentation" level="INFO"/>
    <logger name="org.springdoc.api" level="INFO"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="INFO"/>

    <!-- Application Log Level -->
    <logger name="com.mercaso" level="INFO"/>

    <!-- Environment Specific Configurations -->
    <springProfile name="local">
        <root level="DEBUG">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <springProfile name="integration">
        <root level="ERROR">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>


    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

    <springProfile name="sat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

</configuration>