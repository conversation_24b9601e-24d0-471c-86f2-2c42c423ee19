package com.mercaso.ims.application.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchUpdateItemStatusResultDto extends BaseDto {

    private int updatedCount;

    private List<String> failedSkuNumbers;


}
