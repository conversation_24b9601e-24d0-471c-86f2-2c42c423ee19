package com.mercaso.ims.domain.item.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum AttributeType {
    KEY_ATTRIBUTES,
    SALES_ATTRIBUTES,
    NON_KEY_ATTRIBUTES,
    PRODUCT_ATTRIBUTES,
    UNKNOWN,
    ;

    @JsonCreator
    public static AttributeType fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equalsIgnoreCase(name)).findFirst().orElse(UNKNOWN);
    }
}
