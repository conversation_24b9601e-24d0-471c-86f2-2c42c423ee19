package com.mercaso.ims.application.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemPromoPriceDto extends BaseDto {

    private UUID itemPromoPriceId;

    private BigDecimal crv;

    private BigDecimal promoPrice;

    private BigDecimal promoPriceIndividual;

    private BigDecimal promoPricePlusCrv;

    private Instant promoBeginTime;

    private Instant promoEndTime;

    private Boolean promoFlag;

    private String promoLiveCheck;

    private String promoPricingValidation;

}
