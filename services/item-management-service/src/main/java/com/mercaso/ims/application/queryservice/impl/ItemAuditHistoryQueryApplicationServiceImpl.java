package com.mercaso.ims.application.queryservice.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.dto.ItemAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemBoundToPriceGroupPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemUnboundFromPriceGroupPayloadDto;
import com.mercaso.ims.application.queryservice.ItemAuditHistoryQueryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.enums.EntityEnums;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.service.ItemAdjustmentRequestService;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.service.ItemAdjustmentRequestDetailService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemAuditHistoryQueryApplicationServiceImpl implements ItemAuditHistoryQueryApplicationService {

    private final BusinessEventService businessEventService;

    private final ItemAdjustmentRequestService itemAdjustmentRequestService;

    private final ItemAdjustmentRequestDetailService itemAdjustmentRequestDetailService;

    private final ItemService itemService;

    private final DocumentApplicationService documentApplicationService;


    @Override
    public List<ItemAuditHistoryInfoDto> itemAuditHistories(UUID itemId) {
        List<BusinessEvent> businessEvents = getBusinessEvents(itemId);
        if (CollectionUtils.isEmpty(businessEvents)) {
            return List.of();
        }

        Item item = getItem(itemId);
        var requestMaps = getRequestMaps(item.getSkuNumber());

        return businessEvents.stream()
            .map(event -> buildItemAuditHistoryInfoDto(event, requestMaps.detailMap(), requestMaps.requestMap()))
            .toList();
    }


    private ItemAdjustmentRequestDto buildItemAdjustmentRequestDto(ItemAdjustmentRequest itemAdjustmentRequest) {
        return ItemAdjustmentRequestDto.builder()
            .id(itemAdjustmentRequest.getId())
            .requestFile(itemAdjustmentRequest.getRequestFile())
            .build();
    }


    private List<BusinessEvent> getBusinessEvents(UUID itemId) {
        return businessEventService.findByEntityIdAndType(itemId, EntityEnums.ITEM.getValue());
    }

    private Item getItem(UUID itemId) {
        Item item = itemService.findById(itemId);
        if (item == null) {
            log.error("[getItem] Item not found for id: {}", itemId);
            throw new ImsBusinessException(ITEM_NOT_FOUND.getCode());
        }
        return item;
    }

    private RequestMaps getRequestMaps(String skuNumber) {
        List<ItemAdjustmentRequestDetail> details = itemAdjustmentRequestDetailService.findAllBySku(skuNumber);

        Map<UUID, ItemAdjustmentRequestDetail> detailMap = details.stream()
            .collect(Collectors.toMap(ItemAdjustmentRequestDetail::getId, Function.identity()));

        List<UUID> requestIds = details.stream()
            .map(ItemAdjustmentRequestDetail::getRequestId)
            .distinct()
            .toList();

        Map<UUID, ItemAdjustmentRequest> requestMap = itemAdjustmentRequestService.findByIdIn(requestIds).stream()
            .collect(Collectors.toMap(ItemAdjustmentRequest::getId, Function.identity()));

        return new RequestMaps(detailMap, requestMap);
    }

    private ItemAuditHistoryInfoDto buildItemAuditHistoryInfoDto(
        BusinessEvent businessEvent,
        Map<UUID, ItemAdjustmentRequestDetail> itemAdjustmentRequestDetailMap,
        Map<UUID, ItemAdjustmentRequest> itemAdjustmentRequestMap) {

        UUID itemAdjustmentRequestDetailId;

        switch (businessEvent.getType()) {
            case ITEM_CREATED:
                ItemCreatedPayloadDto itemCreatedPayload = SerializationUtils.deserialize(businessEvent.getPayload(),
                    ItemCreatedPayloadDto.class);

                itemAdjustmentRequestDetailId = itemCreatedPayload.getItemAdjustmentRequestDetailId();

                return populateItemAuditHistoryInfoDto(businessEvent,
                    itemAdjustmentRequestDetailId,
                    itemAdjustmentRequestDetailMap,
                    itemAdjustmentRequestMap, null, itemCreatedPayload.getData());

            case ITEM_AMEND:
                ItemAmendPayloadDto itemAmendPayload = SerializationUtils.deserialize(businessEvent.getPayload(),
                    ItemAmendPayloadDto.class);

                itemAdjustmentRequestDetailId = itemAmendPayload.getItemAdjustmentRequestDetailId();

                return populateItemAuditHistoryInfoDto(businessEvent,
                    itemAdjustmentRequestDetailId,
                    itemAdjustmentRequestDetailMap,
                    itemAdjustmentRequestMap, itemAmendPayload.getData().getPrevious(), itemAmendPayload.getData().getCurrent());

            case ITEM_DELETED:
                ItemDeletedPayloadDto itemDeletedPayload = SerializationUtils.deserialize(businessEvent.getPayload(),
                    ItemDeletedPayloadDto.class);

                itemAdjustmentRequestDetailId = itemDeletedPayload.getItemAdjustmentRequestDetailId();

                return populateItemAuditHistoryInfoDto(businessEvent,
                    itemAdjustmentRequestDetailId,
                    itemAdjustmentRequestDetailMap,
                    itemAdjustmentRequestMap, itemDeletedPayload.getData(), null);
            case ITEM_BOUND_TO_PRICE_GROUP:
                ItemBoundToPriceGroupPayloadDto itemBoundToPriceGroupPayloadDto = SerializationUtils.deserialize(businessEvent.getPayload(),
                    ItemBoundToPriceGroupPayloadDto.class);
                return populateItemAuditHistoryInfoDto(businessEvent,
                    null,
                    itemAdjustmentRequestDetailMap,
                    itemAdjustmentRequestMap, itemBoundToPriceGroupPayloadDto.getData().getPrevious(),
                    itemBoundToPriceGroupPayloadDto.getData().getCurrent());
            case ITEM_UNBOUND_FROM_PRICE_GROUP:
                ItemUnboundFromPriceGroupPayloadDto itemUnboundFromPriceGroupPayloadDto = SerializationUtils.deserialize(
                    businessEvent.getPayload(),
                    ItemUnboundFromPriceGroupPayloadDto.class);

                return populateItemAuditHistoryInfoDto(businessEvent,
                    null,
                    itemAdjustmentRequestDetailMap,
                    itemAdjustmentRequestMap,
                    itemUnboundFromPriceGroupPayloadDto.getData().getPrevious(),
                    itemUnboundFromPriceGroupPayloadDto.getData().getCurrent());
            default:
                return null;
        }
    }

    private ItemAuditHistoryInfoDto populateItemAuditHistoryInfoDto(BusinessEvent businessEvent,
        UUID itemAdjustmentRequestDetailId,
        Map<UUID, ItemAdjustmentRequestDetail> itemAdjustmentRequestDetailMap,
        Map<UUID, ItemAdjustmentRequest> itemAdjustmentRequestMap, ItemDto previous, ItemDto current) {

        ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = itemAdjustmentRequestDetailMap.getOrDefault(
            itemAdjustmentRequestDetailId,
            ItemAdjustmentRequestDetail.builder().build());

        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestMap.getOrDefault(itemAdjustmentRequestDetail.getRequestId(),
            ItemAdjustmentRequest.builder().build());

        return ItemAuditHistoryInfoDto.builder()
            .type(businessEvent.getType())
            .itemAdjustmentRequest(buildItemAdjustmentRequestDto(itemAdjustmentRequest))
            .updatedBy(businessEvent.getCreatedBy())
            .updatedAt(businessEvent.getCreatedAt())
            .updatedUserName(businessEvent.getCreatedUserName())
            .previous(previous)
            .current(current)
            .build();
    }

    private record RequestMaps(
        Map<UUID, ItemAdjustmentRequestDetail> detailMap,
        Map<UUID, ItemAdjustmentRequest> requestMap
    ) {

    }
}
