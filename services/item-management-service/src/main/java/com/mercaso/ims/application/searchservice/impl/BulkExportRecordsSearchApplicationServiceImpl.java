package com.mercaso.ims.application.searchservice.impl;

import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;
import com.mercaso.ims.application.searchservice.BulkExportRecordsSearchApplicationService;
import com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.CustomizedBulkExportRecordsJpaDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class BulkExportRecordsSearchApplicationServiceImpl implements BulkExportRecordsSearchApplicationService {

    private final CustomizedBulkExportRecordsJpaDao customizedBulkExportRecordsJpaDao;

    @Override
    public BulkExportRecordsSearchDto searchBulkExportRecords(BulkExportRecordsQuery bulkExportRecordsQuery) {
        log.info("Searching bulk export records with query: {}", bulkExportRecordsQuery);
        return customizedBulkExportRecordsJpaDao.getBulkExportRecords(bulkExportRecordsQuery);
    }
}
