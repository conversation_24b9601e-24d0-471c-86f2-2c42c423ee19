package com.mercaso.ims.application.dto;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BulkExportRecordsDto extends BaseDto {

    private UUID id;

    private String fileName;

    private Instant searchTime;

    private Instant sendEmailTime;

    private String customFilter;

    private String exportBy;

    private Instant createdAt;
}
