package com.mercaso.ims.domain;

import java.io.Serializable;

/**
 * A value object
 */
public interface ValueObject<T> extends Serializable {


    /**
     * Value objects compare by the values of their attributes, they don't have an identity.
     *
     * @param other the other value object.
     * @return <code>true</code> if the given value objects' and this value objects' attributes are the same.
     */
    boolean sameValueAs(T other);
}
