package com.mercaso.ims.domain.itemadjustmentrequest.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.mercaso.ims.infrastructure.statemachine.StateType;
import java.util.Arrays;

public enum ItemAdjustmentRequestStatus implements StateType {

    UPLOADED,
    FILE_PROCESSED,
    COMPLETED,
    FAILURE,
    UNKNOWN,
    ;

    @JsonCreator
    public static ItemAdjustmentRequestStatus fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }
}
