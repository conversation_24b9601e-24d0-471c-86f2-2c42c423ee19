package com.mercaso.ims.infrastructure.excel.processor;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.application.service.VendorApplicationService;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateOrUpdateItemRequestData;
import com.mercaso.ims.infrastructure.excel.data.CreateVendorRequestData;
import com.mercaso.ims.infrastructure.excel.data.DeleteItemRequestData;
import com.mercaso.ims.infrastructure.excel.data.RemoveUpcRequestData;
import com.mercaso.ims.infrastructure.excel.data.UpdateVendorItemsRequestData;
import com.mercaso.ims.infrastructure.excel.listener.CreateItemRequestDataListener;
import com.mercaso.ims.infrastructure.excel.listener.CreateVendorRequestDataListener;
import com.mercaso.ims.infrastructure.excel.listener.DeleteItemRequestDataListener;
import com.mercaso.ims.infrastructure.excel.listener.RemoveUpcRequestDataListener;
import com.mercaso.ims.infrastructure.excel.listener.UpdateItemRequestDataListener;
import com.mercaso.ims.infrastructure.excel.listener.UpdateVendorItemsRequestDataListener;
import java.io.ByteArrayInputStream;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ItemAdjustmentRequestTemplateSheetProcessor {

    private final ItemRepository itemRepository;

    private final ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    private final ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    private final DocumentApplicationService documentApplicationService;
    private final VendorApplicationService vendorApplicationService;
    private final VendorRepository vendorRepository;
    private final ItemRegPriceService itemRegPriceService;
    private final VendorItemRepository vendorItemRepository;
    private final CategoryApplicationService categoryApplicationService;
    private final FeatureFlagsManager featureFlagsManager;

    public void process(ItemAdjustmentRequestDto requestDto) {

        UUID requestId = requestDto.getId();
        String requestFile = requestDto.getRequestFile();
        byte[] document = documentApplicationService.downloadDocument(requestFile);
        try (ExcelReader excelReader = EasyExcelFactory.read(new ByteArrayInputStream(document)).build()) {
            ReadSheet createVendorRequestSheet =
                EasyExcelFactory.readSheet("New Vendor")
                    .head(CreateVendorRequestData.class)
                    .registerReadListener(new CreateVendorRequestDataListener(requestId,
                        vendorApplicationService,
                        vendorRepository, itemAdjustmentRequestDetailApplicationService))
                    .build();

            ReadSheet createItemRequestSheet =
                EasyExcelFactory.readSheet("New Items")
                    .head(CreateOrUpdateItemRequestData.class)
                    .registerReadListener(new CreateItemRequestDataListener(requestId,
                        itemAdjustmentRequestDetailApplicationService,
                        itemAdjustmentRequestApplicationService,
                        itemRepository,
                        vendorRepository, vendorItemRepository,
                        categoryApplicationService, featureFlagsManager))
                    .build();
            ReadSheet updateItemRequestSheet =
                EasyExcelFactory.readSheet("Update Items")
                    .head(CreateOrUpdateItemRequestData.class)
                    .registerReadListener(new UpdateItemRequestDataListener(requestId,
                        itemAdjustmentRequestDetailApplicationService,
                        itemAdjustmentRequestApplicationService,
                        itemRepository,
                        vendorRepository, itemRegPriceService, vendorItemRepository,
                        categoryApplicationService, featureFlagsManager))
                    .build();
            ReadSheet updateVendorItemsRequestSheet =
                EasyExcelFactory.readSheet("Update Vendor Items")
                    .head(UpdateVendorItemsRequestData.class)
                    .registerReadListener(new UpdateVendorItemsRequestDataListener(requestId,
                        itemAdjustmentRequestDetailApplicationService,
                        itemAdjustmentRequestApplicationService,
                        itemRepository,
                        vendorRepository, vendorItemRepository,
                        categoryApplicationService, featureFlagsManager))
                    .build();
            ReadSheet deleteItemRequestSheet =
                EasyExcelFactory.readSheet("Delete Item")
                    .head(DeleteItemRequestData.class)
                    .registerReadListener(new DeleteItemRequestDataListener(requestId,
                        itemAdjustmentRequestDetailApplicationService,
                        itemAdjustmentRequestApplicationService,
                        itemRepository,
                        vendorRepository, vendorItemRepository,
                        categoryApplicationService, featureFlagsManager))
                    .build();

            ReadSheet removeUpcRequestSheet =
                EasyExcelFactory.readSheet("Remove UPC")
                    .head(RemoveUpcRequestData.class)
                    .registerReadListener(new RemoveUpcRequestDataListener(requestId,
                        itemAdjustmentRequestDetailApplicationService,
                        itemAdjustmentRequestApplicationService,
                        itemRepository,
                        vendorRepository, vendorItemRepository,
                        categoryApplicationService, featureFlagsManager))
                    .build();
            excelReader.read(createVendorRequestSheet,
                createItemRequestSheet,
                updateItemRequestSheet,
                updateVendorItemsRequestSheet,
                deleteItemRequestSheet,
                removeUpcRequestSheet);
            itemAdjustmentRequestApplicationService.finishProcessed(requestId);
        }

    }
}
