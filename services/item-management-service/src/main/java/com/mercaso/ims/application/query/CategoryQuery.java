package com.mercaso.ims.application.query;

import com.mercaso.ims.domain.category.enums.CategoryStatus;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class CategoryQuery {

    private UUID categoryId;

    private UUID ancestorCategoryId;

    private Integer depth;

    private CategoryStatus status;

    private String leafCategoryName;

    private List<UUID> categoryIds;
}
