package com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa;

import static com.mercaso.ims.application.query.BulkExportRecordsQuery.SortType.CREATED_AT_DESC;

import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedBulkExportRecordsJpaDaoImpl implements CustomizedBulkExportRecordsJpaDao {

    private final JdbcTemplate jdbcTemplate;

    // SQL queries
    private static final String BASE_QUERY = """
        SELECT
            ber.id,
            ber.file_name,
            ber.search_time,
            ber.send_email_time,
            ber.custom_filter,
            ber.created_at,
            ber.created_by,
            ber.created_user_name,
            ber.updated_at,
            ber.updated_by,
            ber.updated_user_name
        FROM bulk_export_records ber
        WHERE ber.deleted_at IS NULL
        """;

    private static final String COUNT_QUERY = """
        SELECT COUNT(*)
        FROM bulk_export_records ber
        WHERE ber.deleted_at IS NULL
        """;

    @Override
    public BulkExportRecordsSearchDto getBulkExportRecords(BulkExportRecordsQuery query) {
        log.info("Querying bulk export records with parameters: {}", query);

        if (query == null) {
            log.warn("Query is null, returning empty result");
            return BulkExportRecordsSearchDto.builder()
                .data(List.of())
                .totalCount(0L)
                .build();
        }

        try {
            // Build dynamic WHERE clause
            StringBuilder whereClause = new StringBuilder();
            List<Object> parameters = new ArrayList<>();
            buildWhereClause(query, whereClause, parameters);

            // Build ORDER BY clause
            String orderByClause = buildOrderByClause(query);

            // Execute count query
            long totalCount = executeCountQuery(whereClause.toString(), parameters);

            // Execute data query with pagination
            List<BulkExportRecordsDto> data = executeDataQuery(whereClause.toString(), orderByClause, query, parameters);

            log.info("Successfully retrieved {} bulk export records out of {} total", data.size(), totalCount);

            return BulkExportRecordsSearchDto.builder()
                .data(data)
                .totalCount(totalCount)
                .build();

        } catch (Exception e) {
            log.error("Error querying bulk export records: {}", e.getMessage(), e);
            return BulkExportRecordsSearchDto.builder()
                .data(List.of())
                .totalCount(0L)
                .build();
        }
    }

    /**
     * Builds the WHERE clause based on query parameters.
     */
    private void buildWhereClause(BulkExportRecordsQuery query, StringBuilder whereClause, List<Object> parameters) {
        boolean hasConditions = false;

        // Filter by created date range
        if (query.getCreatedStartDate() != null) {
            if (hasConditions) {
                whereClause.append(" AND ");
            }
            whereClause.append("ber.created_at >= ?");
            parameters.add(Timestamp.from(query.getCreatedStartDate()));
            hasConditions = true;
        }

        if (query.getCreatedEndDate() != null) {
            if (hasConditions) {
                whereClause.append(" AND ");
            }
            whereClause.append("ber.created_at <= ?");
            parameters.add(Timestamp.from(query.getCreatedEndDate()));
            hasConditions = true;
        }

        // Filter by created user name
        if (StringUtils.hasText(query.getCreatedUserName())) {
            if (hasConditions) {
                whereClause.append(" AND ");
            }
            whereClause.append("ber.created_user_name ILIKE ?");
            parameters.add("%" + query.getCreatedUserName().trim() + "%");
            hasConditions = true;
        }
    }

    /**
     * Builds the ORDER BY clause based on sort parameters.
     */
    private String buildOrderByClause(BulkExportRecordsQuery query) {
        if (query.getSort() != null) {
            return switch (query.getSort()) {
                case CREATED_AT_DESC -> " ORDER BY ber.created_at DESC";
                default -> " ORDER BY ber.created_at DESC";
            };
        }
        return " ORDER BY ber.created_at DESC"; // Default sort
    }

    /**
     * Executes the count query to get total number of records.
     */
    private long executeCountQuery(String whereClause, List<Object> parameters) {
        StringBuilder sql = new StringBuilder(COUNT_QUERY);
        if (!whereClause.isEmpty()) {
            sql.append(" AND ").append(whereClause);
        }

        return jdbcTemplate.queryForObject(sql.toString(), Long.class, parameters.toArray());
    }

    /**
     * Executes the data query with pagination.
     */
    private List<BulkExportRecordsDto> executeDataQuery(String whereClause, String orderByClause,
                                                       BulkExportRecordsQuery query, List<Object> parameters) {
        StringBuilder sql = new StringBuilder(BASE_QUERY);
        if (!whereClause.isEmpty()) {
            sql.append(" AND ").append(whereClause);
        }
        sql.append(orderByClause);
        sql.append(query.offsetLimitSql());

        return jdbcTemplate.query(sql.toString(), new BulkExportRecordsRowMapper(), parameters.toArray());
    }

    /**
     * Row mapper for converting ResultSet to BulkExportRecordsDto.
     */
    private static class BulkExportRecordsRowMapper implements RowMapper<BulkExportRecordsDto> {
        @Override
        public BulkExportRecordsDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            BulkExportRecordsDto dto = BulkExportRecordsDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .fileName(rs.getString("file_name"))
                .searchTime(getInstantFromTimestamp(rs, "search_time"))
                .sendEmailTime(getInstantFromTimestamp(rs, "send_email_time"))
                .customFilter(rs.getString("custom_filter"))
                .exportBy(rs.getString("created_user_name"))
                .createdAt(getInstantFromTimestamp(rs, "created_at"))
                .build();

            return dto;
        }

        private Instant getInstantFromTimestamp(ResultSet rs, String columnName) throws SQLException {
            Timestamp timestamp = rs.getTimestamp(columnName);
            return timestamp != null ? timestamp.toInstant() : null;
        }
    }
}
