package com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa;

import static com.mercaso.ims.application.query.BulkExportRecordsQuery.SortType.CREATED_AT_DESC;

import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedBulkExportRecordsJpaDaoImpl implements CustomizedBulkExportRecordsJpaDao {

    private final JdbcTemplate jdbcTemplate;

    // SQL queries
    private static final String BASE_QUERY = """
        SELECT
            ber.id,
            ber.file_name,
            ber.search_time,
            ber.send_email_time,
            ber.custom_filter,
            ber.created_at,
            ber.created_by,
            ber.created_user_name,
            ber.updated_at,
            ber.updated_by,
            ber.updated_user_name
        FROM bulk_export_records ber
        WHERE ber.deleted_at IS NULL
        """;

    private static final String COUNT_QUERY = """
        SELECT COUNT(*)
        FROM bulk_export_records ber
        WHERE ber.deleted_at IS NULL
        """;

    @Override
    public BulkExportRecordsSearchDto getBulkExportRecords(BulkExportRecordsQuery query) {
        log.info("Querying bulk export records with parameters: {}", query);

        if (query == null) {
            log.warn("Query is null, returning empty result");
            return BulkExportRecordsSearchDto.builder()
                .data(List.of())
                .totalCount(0L)
                .build();
        }

        try {
            // Execute count query
            long totalCount = executeCountQuery(query);

            // Execute data query with pagination
            List<BulkExportRecordsDto> data = executeDataQuery(query);

            log.info("Successfully retrieved {} bulk export records out of {} total", data.size(), totalCount);

            return BulkExportRecordsSearchDto.builder()
                .data(data)
                .totalCount(totalCount)
                .build();

        } catch (Exception e) {
            log.error("Error querying bulk export records: {}", e.getMessage(), e);
            return BulkExportRecordsSearchDto.builder()
                .data(List.of())
                .totalCount(0L)
                .build();
        }
    }

    /**
     * Builds the ORDER BY clause based on sort parameters.
     */
    private String buildOrderByClause(BulkExportRecordsQuery query) {
        String sql = "";
        if (query.getSort() == null || query.getSort() == CREATED_AT_DESC) {
            return " ORDER BY ber.created_at DESC ";
        }
        return sql;
    }

    /**
     * Executes the count query to get total number of records.
     */
    private long executeCountQuery(BulkExportRecordsQuery query) {
        StringBuilder queryStr = new StringBuilder(COUNT_QUERY);

        queryStr.append(this.buildCreateTimeCondition(query.getCreatedStartDate(), query.getCreatedEndDate()));
        queryStr.append(this.buildCreateUserNameCondition(query.getCreatedUserName()));

        List<Long> count = jdbcTemplate.query(queryStr.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                if (!ObjectUtils.isEmpty(query.getCreatedStartDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedStartDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedEndDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedEndDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedUserName())) {
                    ps.setString(index, "%" + query.getCreatedUserName() + "%");
                }
            }
        }, (rs, rowNum) -> rs.getLong(1));
        return count.getFirst();
    }

    /**
     * Executes the data query with pagination.
     */
    private List<BulkExportRecordsDto> executeDataQuery(BulkExportRecordsQuery query) {

        // Build dynamic WHERE clause
        StringBuilder queryStr = new StringBuilder(BASE_QUERY);

        queryStr.append(this.buildCreateTimeCondition(query.getCreatedStartDate(), query.getCreatedEndDate()));
        queryStr.append(this.buildCreateUserNameCondition(query.getCreatedUserName()));
        queryStr.append(this.buildOrderByClause(query));
        queryStr.append(query.offsetLimitSql());

        return jdbcTemplate.query(queryStr.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                if (!ObjectUtils.isEmpty(query.getCreatedStartDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedStartDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedEndDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedEndDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedUserName())) {
                    ps.setString(index, "%" + query.getCreatedUserName() + "%");
                }
            }
        }, new BulkExportRecordsRowMapper());
    }

    /**
     * Row mapper for converting ResultSet to BulkExportRecordsDto.
     */
    private static class BulkExportRecordsRowMapper implements RowMapper<BulkExportRecordsDto> {
        @Override
        public BulkExportRecordsDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            return BulkExportRecordsDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .fileName(rs.getString("file_name"))
                .searchTime(getInstantFromTimestamp(rs, "search_time"))
                .sendEmailTime(getInstantFromTimestamp(rs, "send_email_time"))
                .customFilter(rs.getString("custom_filter"))
                .exportBy(rs.getString("created_user_name"))
                .createdAt(getInstantFromTimestamp(rs, "created_at"))
                .build();
        }

        private Instant getInstantFromTimestamp(ResultSet rs, String columnName) throws SQLException {
            Timestamp timestamp = rs.getTimestamp(columnName);
            return timestamp != null ? timestamp.toInstant() : null;
        }
    }

    private String buildCreateTimeCondition(Instant createdStartDate, Instant createdEndDate) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!ObjectUtils.isEmpty(createdStartDate)) {
            stringBuilder.append(" AND ber.created_at >= ? ");
        }
        if (!ObjectUtils.isEmpty(createdEndDate)) {
            stringBuilder.append(" AND ber.created_at <= ? ");
        }
        return stringBuilder.toString();
    }

    private String buildCreateUserNameCondition(String createUserName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(createUserName)) {
            return " AND ber.created_user_name ILIKE ? ";
        }
        return "";
    }
}
