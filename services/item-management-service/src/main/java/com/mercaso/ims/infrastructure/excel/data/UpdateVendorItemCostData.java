package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UpdateVendorItemCostData extends ItemAdjustmentRequestData {

    @ExcelProperty("Supplier's SKU")
    private String vendorItemNumber;
    @ExcelProperty("Item Name")
    private String vendorItemName;
    @ExcelProperty("UPC")
    private String upc;
    @ExcelProperty("Unit Cost")
    private BigDecimal unitCost;

    private String sku;

    @Override
    public String getSku() {
        return "";
    }
}
