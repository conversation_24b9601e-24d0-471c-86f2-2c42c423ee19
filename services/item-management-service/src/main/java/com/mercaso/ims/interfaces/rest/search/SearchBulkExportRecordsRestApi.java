package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;
import com.mercaso.ims.application.searchservice.BulkExportRecordsSearchApplicationService;
import jakarta.validation.constraints.Min;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping(value = "/v1/search/bulk-export-records", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
@Validated
public class SearchBulkExportRecordsRestApi {

    private final BulkExportRecordsSearchApplicationService bulkExportRecordsSearchApplicationService;


    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:items')")
    public BulkExportRecordsSearchDto searchBulkExportRecords(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        int pageSize,
        @RequestParam(value = "createdStartDate", required = false) Instant createdStartDate,
        @RequestParam(value = "createdEndDate", required = false) Instant createdEndDate,
        @RequestParam(value = "createdUserName", required = false) String createdUserName) {

        log.info(
            "[searchBulkExportRecords] param createdStartDate: {}, created EndDat: {}, createdUserName: {}.",
            createdStartDate,
            createdEndDate,
            createdUserName);
        return bulkExportRecordsSearchApplicationService.searchBulkExportRecords(BulkExportRecordsQuery
            .builder()
            .page(page)
            .pageSize(pageSize)
            .createdUserName(createdUserName)
            .createdStartDate(createdStartDate)
            .createdEndDate(createdEndDate)
            .build());

    }

}
