package com.mercaso.ims.application.mapper.itemversion;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemVersionDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ItemVersionDtoApplicationMapper extends BaseDtoApplicationMapper<ItemVersion, ItemVersionDto> {

    @Override
    @Mapping(expression = "java(mapItemDataToItemDto(domain.getItemData()))", target = "itemDto")
    ItemVersionDto domainToDto(ItemVersion domain);

    default ItemDto mapItemDataToItemDto(String itemData) {
        if (itemData != null) {
            return SerializationUtils.deserialize(itemData, ItemDto.class);
        }
        return null;
    }
}
