package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UpdateVendorItemsRequestData extends ItemAdjustmentRequestData {

    @ExcelProperty("SKU")
    private String sku;
    @ExcelProperty("Vendor")
    private String vendor;
    @ExcelProperty("Vendor Item Number")
    private String vendorItemNumber;
    @ExcelProperty("Direct Vendor Item Cost")
    private BigDecimal poVendorItemCost;
    @ExcelProperty("JIT Vendor Item Cost")
    private BigDecimal jitVendorItemCost;
    @ExcelProperty("Vendor Aisle")
    private String vendorAisle;
    @ExcelProperty("Vendor Item Availability")
    private Boolean vendorItemAvailability;
    @ExcelProperty("Vendor Item Type")
    private String vendorItemType;


    public boolean haveVendorItemInfo() {
        return poVendorItemCost != null || StringUtils.isNotBlank(vendorItemNumber)
            || StringUtils.isNotBlank(vendorAisle) || vendorItemAvailability != null || jitVendorItemCost != null;
    }

    public boolean haveVendorName() {
        return StringUtils.isNotBlank(vendor);
    }


    public boolean haveVendorItemType() {
        return StringUtils.isNotBlank(vendorItemType);
    }

    public String getVendorItemType() {
        if (null != vendorItemType) {
            vendorItemType = vendorItemType.replaceAll("\\s+", "").toUpperCase();
        }
        return vendorItemType;
    }

    public boolean haveVendorItemCostInfo() {
        return poVendorItemCost != null || jitVendorItemCost != null;
    }
}