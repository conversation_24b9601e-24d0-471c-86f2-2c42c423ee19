package com.mercaso.ims.infrastructure.event.factory;

import com.mercaso.ims.application.dto.BaseDto;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class BusinessEventPayloadFactory<P extends BusinessEventPayloadDto> {

    protected abstract BusinessEventPayloadDto<? extends BaseDto> build(BusinessEvent businessEvent);

    public abstract Class<P> payloadClass();

    public Optional<BusinessEventPayloadDto<? extends BaseDto>> buildPayloadDto(BusinessEvent businessEvent) {
        BusinessEventPayloadDto<? extends BaseDto> payloadDto = this.build(businessEvent);

        if (payloadDto != null) {
            return Optional.of(payloadDto);
        }

        return Optional.empty();
    }

}
