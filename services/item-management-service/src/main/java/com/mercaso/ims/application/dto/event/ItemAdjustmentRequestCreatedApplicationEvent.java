package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCreatedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemAdjustmentRequestCreatedApplicationEvent extends BaseApplicationEvent<ItemAdjustmentRequestCreatedPayloadDto> {

    public ItemAdjustmentRequestCreatedApplicationEvent(Object source, ItemAdjustmentRequestCreatedPayloadDto payload) {
        super(source, payload);
    }
}