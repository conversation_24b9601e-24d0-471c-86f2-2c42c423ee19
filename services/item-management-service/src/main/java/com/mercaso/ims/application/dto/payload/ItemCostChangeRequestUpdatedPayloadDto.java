package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemCostChangeRequestUpdatedPayloadDto extends BusinessEventPayloadDto<ItemCostChangeRequestDto> {

    private UUID itemCostChangeRequestId;

    @Builder
    public ItemCostChangeRequestUpdatedPayloadDto(ItemCostChangeRequestDto data, UUID itemCostChangeRequestId) {
        super(data);
        this.itemCostChangeRequestId = itemCostChangeRequestId;
    }
}