package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.service.BulkExportRecordsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class SearchBulkExportRecordsRestApiIT extends AbstractIT {

    @Autowired
    private BulkExportRecordsService bulkExportRecordsService;

    private BulkExportRecords testRecord;
    private String testUserName = "testUser";

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testRecord = BulkExportRecords.builder()
            .fileName("test_export.csv")
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now().plusSeconds(60))
            .customFilter("{\"key\":\"value\"}")
            .exportBy(testUserName)
            .build();

        bulkExportRecordsService.save(testRecord);
    }

    @Test
    void searchBulkExportRecords_WithDefaultParams_ShouldReturnResults() throws Exception {
        // When
        MvcResult result = mockMvc.perform(get("/v1/search/bulk-export-records")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andReturn();

        // Then
        BulkExportRecordsSearchDto response = objectMapper.readValue(
            result.getResponse().getContentAsString(),
            BulkExportRecordsSearchDto.class
        );

        assertNotNull(response);
        assertTrue(response.getTotalCount() > 0);
        assertFalse(response.getData().isEmpty());
    }

    @Test
    void searchBulkExportRecords_WithPagination_ShouldReturnCorrectPage() throws Exception {
        // When
        MvcResult result = mockMvc.perform(get("/v1/search/bulk-export-records")
                .param("page", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        // Then
        BulkExportRecordsSearchDto response = objectMapper.readValue(
            result.getResponse().getContentAsString(),
            BulkExportRecordsSearchDto.class
        );

        assertNotNull(response);
        assertTrue(response.getData().size() <= 10);
    }

    @Test
    void searchBulkExportRecords_WithDateRange_ShouldFilterByDate() throws Exception {
        // Given
        Instant startDate = Instant.now().minusSeconds(3600);
        Instant endDate = Instant.now().plusSeconds(3600);

        // When
        MvcResult result = mockMvc.perform(get("/v1/search/bulk-export-records")
                .param("createdStartDate", startDate.toString())
                .param("createdEndDate", endDate.toString())
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        // Then
        BulkExportRecordsSearchDto response = objectMapper.readValue(
            result.getResponse().getContentAsString(),
            BulkExportRecordsSearchDto.class
        );

        assertNotNull(response);
        assertTrue(response.getTotalCount() > 0);
    }

    @Test
    void searchBulkExportRecords_WithUserName_ShouldFilterByUser() throws Exception {
        // When
        MvcResult result = mockMvc.perform(get("/v1/search/bulk-export-records")
                .param("createdUserName", testUserName)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        // Then
        BulkExportRecordsSearchDto response = objectMapper.readValue(
            result.getResponse().getContentAsString(),
            BulkExportRecordsSearchDto.class
        );

        assertNotNull(response);
        assertTrue(response.getTotalCount() > 0);
        response.getData().forEach(record ->
            assertEquals(testUserName, record.getExportBy())
        );
    }

    @Test
    @WithMockUser(authorities = "invalid:authority")
    void searchBulkExportRecords_WithoutRequiredAuthority_ShouldReturnForbidden() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/search/bulk-export-records")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isForbidden());
    }

    @Test
    void searchBulkExportRecords_WithInvalidPage_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/search/bulk-export-records")
                .param("page", "0")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }

    @Test
    void searchBulkExportRecords_WithInvalidPageSize_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/search/bulk-export-records")
                .param("pageSize", "0")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }

    @Test
    void searchBulkExportRecords_WithInvalidDateRange_ShouldReturnBadRequest() throws Exception {
        // Given
        Instant startDate = Instant.now().plusSeconds(3600);
        Instant endDate = Instant.now().minusSeconds(3600);

        // When & Then
        mockMvc.perform(get("/v1/search/bulk-export-records")
                .param("createdStartDate", startDate.toString())
                .param("createdEndDate", endDate.toString())
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }
}